# Zentao MCP Server - 管理员权限流程说明

## 🔐 管理员权限流程完整解决方案

本项目已实现完整的管理员权限管理流程，解决了"创建Key需要管理员权限，但管理员账户从哪里来"的问题。

## 📋 权限流程设计

### 1. **系统初始化阶段**
- 使用环境变量配置默认管理员账户
- 系统启动时自动创建默认管理员
- 支持手动运行初始化脚本

### 2. **管理员认证流程**
- Web界面：用户名/密码登录 → 会话令牌
- API访问：支持Super Admin Key直接访问

### 3. **权限层级**
```
管理员 (Admin)
├── 创建/删除/管理 API Key
├── 管理其他用户账户
└── 系统配置管理

普通用户
└── 使用 API Key 访问服务
```

## 🚀 快速开始

### 1. **环境配置**

复制环境变量模板：
```bash
cd zentao-mcp-backend-service
cp .env.example .env
```

编辑 `.env` 文件：
```bash
# 管理员配置 (必须修改)
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-password
ADMIN_EMAIL=<EMAIL>

# 数据库配置
DATABASE_URL=sqlite:///./data/zentao_mcp.db

# 禅道API配置
ZENTAO_BASE_URL=http://your-zentao-server.com
```

### 2. **系统初始化**

#### 方法一：运行初始化脚本
```bash
cd zentao-mcp-backend-service
python manage.py init
```

#### 方法二：启动服务自动初始化
```bash
cd zentao-mcp-backend-service
uv run uvicorn main:app --host 0.0.0.0 --port 8000
```

### 3. **启动Web管理界面**
```bash
cd zentao-token-web
bun install
bun run dev
```

访问：http://localhost:3000

## 🔑 管理员登录流程

### 1. **Web界面登录**
1. 访问 http://localhost:3000
2. 使用默认账户登录：
   - 用户名：`admin`
   - 密码：`admin123` (或您在.env中设置的密码)
3. 登录成功后可以：
   - 创建API Key
   - 管理现有Key
   - 查看使用统计



## 📁 项目结构

```
zentaomcpserver/
├── zentao-mcp-backend-service/     # 后端服务
│   ├── app/
│   │   ├── models/admin.py         # 管理员用户模型
│   │   ├── services/auth_service.py # 认证服务
│   │   ├── api/v1/endpoints/auth.py # 认证API
│   │   └── core/init.py            # 系统初始化
│   ├── manage.py                   # 统一管理脚本
│   └── .env.example                # 环境变量模板
├── zentao-token-web/               # Web管理界面
│   ├── src/
│   │   ├── services/auth.ts        # 前端认证服务
│   │   ├── views/Login.vue         # 登录页面
│   │   └── router/index.ts         # 路由配置
└── README.md                       # 本文档
```

## 🔒 安全特性

### 1. **密码安全**
- 使用SHA-256哈希存储密码
- 支持会话令牌管理
- 自动过期机制

### 2. **API Key管理**
- 哈希存储，不保存明文
- 支持激活/禁用状态
- 创建时一次性显示明文

### 3. **访问控制**
- 路由级别的认证检查
- 会话自动过期
- 统一的错误处理

## 🛠️ 开发和部署

### 开发环境
```bash
# 后端
cd zentao-mcp-backend-service
uv sync
uv run uvicorn main:app --reload

# 前端
cd zentao-token-web
bun install
bun run dev
```

### 生产部署
```bash
# 使用Docker Compose
docker-compose up -d
```

## ❓ 常见问题

### Q: 忘记管理员密码怎么办？
A: 可以通过以下方式重置：
1. 修改 `.env` 文件中的 `ADMIN_PASSWORD`
2. 重新运行 `python manage.py init`
3. 或者使用 `python manage.py reset` 重置整个系统

### Q: 如何添加新的管理员？
A: 管理员可以通过Web界面的用户管理功能来创建新的管理员账户。

### Q: API Key忘记了怎么办？
A: API Key创建后只显示一次，忘记后需要：
1. 在Web界面禁用旧Key
2. 创建新的API Key

## 🎯 总结

通过这套完整的权限管理流程，我们解决了：

✅ **初始管理员创建问题** - 通过环境变量和系统初始化  
✅ **管理员登录认证** - Web界面 + 会话管理  
✅ **API Key生命周期管理** - 创建、禁用、删除  
✅ **安全性保障** - 密码哈希、令牌过期、访问控制  
✅ **用户体验** - 直观的Web界面、清晰的操作流程  

现在系统可以安全地启动和使用，管理员可以通过Web界面方便地管理API Key，普通用户可以使用API Key访问服务。