
# 禅道MCP权限系统 - 管理员指南

## 🔧 系统部署

### 环境要求
- **Python**: 3.8+
- **Node.js**: 16+
- **数据库**: SQLite（默认）或PostgreSQL
- **操作系统**: Linux/macOS/Windows

### 安装步骤

#### 1. 后端服务部署
```bash
# 克隆代码
git clone <repository_url>
cd zentaomcpserver

# 安装Python依赖
cd zentao-mcp-backend-service
pip install -r requirements.txt

# 初始化数据库
python init_admin.py

# 启动服务
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
```

#### 2. 前端服务部署
```bash
# 安装依赖
cd zentao-token-web
npm install  # 或 bun install

# 构建生产版本
npm run build

# 启动开发服务器
npm run dev
```

### 配置管理

#### 环境变量
```bash
# 数据库配置
DATABASE_URL=sqlite:///./zentao_mcp.db

# JWT配置
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS配置
ALLOWED_ORIGINS=http://localhost:3000
```

#### 配置文件
- **后端配置**: `app/core/config.py`
- **前端配置**: `.env` 文件

## 👥 用户管理

### 初始化管理员
```bash
# 创建超级管理员
python create_admin.py --username admin --password admin123 --email <EMAIL>
```

### 批量用户操作
```python
# 批量创建用户脚本示例
import requests

users = [
    {"username": "user1", "email": "<EMAIL>", "password": "pass123", "user_type": "USER"},
    {"username": "user2", "email": "<EMAIL>", "password": "pass123", "user_type": "ADMIN"}
]

for user in users:
    response = requests.post("http://localhost:8000/api/v1/users", json=user, headers=headers)
    print(f"Created user: {user['username']}")
```

### 用户权限管理
- **权限提升**: 将普通用户提升为管理员
- **权限降级**: 降低用户权限级别
- **批量操作**: 批量修改用户状态

## 🔑 API密钥管理

### 系统级API密钥
```bash
# 创建系统级API密钥
curl -X POST http://localhost:8000/api/v1/api-keys   -H "Authorization: Bearer ADMIN_TOKEN"   -d '{"name":"System Key","permissions":["admin"]}'
```

### 密钥轮换策略
- **定期轮换**: 建议每90天轮换一次
- **自动化脚本**: 编写自动轮换脚本
- **通知机制**: 密钥更新时通知相关用户

## 📊 监控和维护

### 系统监控
```bash
# 检查系统状态
curl http://localhost:8000/health

# 查看系统指标
curl http://localhost:8000/metrics
```

### 日志管理
- **应用日志**: `logs/app.log`
- **访问日志**: `logs/access.log`
- **错误日志**: `logs/error.log`

### 数据库维护
```sql
-- 清理过期会话
DELETE FROM user_sessions WHERE expires_at < datetime('now');

-- 清理旧审计日志（保留90天）
DELETE FROM audit_logs WHERE created_at < datetime('now', '-90 days');

-- 数据库优化
VACUUM;
ANALYZE;
```

## 🛡️ 安全配置

### HTTPS配置
```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:3000;
    }
    
    location /api {
        proxy_pass http://localhost:8000;
    }
}
```

### 防火墙配置
```bash
# 只允许必要端口
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw deny 8000   # 直接访问后端
ufw deny 3000   # 直接访问前端
```

### 安全加固
- **定期更新依赖**
- **启用请求限制**
- **配置CORS策略**
- **启用审计日志**

## 📈 性能优化

### 数据库优化
```sql
-- 创建索引
CREATE INDEX idx_users_username ON admin_users(username);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX idx_api_keys_user_id ON api_keys(user_id);
```

### 缓存配置
```python
# Redis缓存配置
REDIS_URL = "redis://localhost:6379/0"
CACHE_TTL = 300  # 5分钟
```

### 负载均衡
```yaml
# Docker Compose示例
version: '3.8'
services:
  app1:
    build: .
    ports:
      - "8001:8000"
  app2:
    build: .
    ports:
      - "8002:8000"
  nginx:
    image: nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
```

## 🔄 备份和恢复

### 数据备份
```bash
#!/bin/bash
# 备份脚本
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/zentao_mcp"

# 备份数据库
cp zentao_mcp.db "$BACKUP_DIR/db_$DATE.db"

# 备份配置文件
tar -czf "$BACKUP_DIR/config_$DATE.tar.gz" app/core/config.py .env

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "*.db" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

### 数据恢复
```bash
# 恢复数据库
cp /backup/zentao_mcp/db_20240829_120000.db zentao_mcp.db

# 重启服务
systemctl restart zentao-mcp
```

## 🚨 故障处理

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查数据库文件权限
ls -la zentao_mcp.db

# 重新创建数据库
python init_admin.py --force
```

#### 2. 内存使用过高
```bash
# 检查进程状态
ps aux | grep uvicorn

# 重启服务
systemctl restart zentao-mcp
```

#### 3. 登录失败
```python
# 重置管理员密码
from app.services.password_service import PasswordService
from app.models.admin import AdminUser
from app.core.database import SessionLocal

db = SessionLocal()
admin = db.query(AdminUser).filter(AdminUser.username == "admin").first()
admin.password_hash = PasswordService.hash_password("new_password")
db.commit()
```

### 紧急恢复程序
1. **停止所有服务**
2. **恢复最新备份**
3. **验证数据完整性**
4. **重启服务**
5. **验证功能正常**

## 📋 维护清单

### 日常维护（每日）
- [ ] 检查系统状态
- [ ] 查看错误日志
- [ ] 监控资源使用

### 周期维护（每周）
- [ ] 清理临时文件
- [ ] 检查磁盘空间
- [ ] 更新安全补丁

### 定期维护（每月）
- [ ] 数据库优化
- [ ] 备份验证
- [ ] 性能分析
- [ ] 安全审计

### 年度维护
- [ ] 系统升级
- [ ] 硬件检查
- [ ] 灾难恢复演练
- [ ] 安全评估
