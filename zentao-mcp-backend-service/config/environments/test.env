# 测试环境特定配置
# 只包含与通用配置不同的项目

# 环境标识
APP_VERSION=test
ENVIRONMENT=testing

# 测试环境数据库
DATABASE_URL=sqlite:///./data/zentao_mcp_test.db
DATABASE_ECHO=false

# 测试环境服务配置
WORKERS=2
RELOAD=false

# 测试环境安全配置
ADMIN_API_KEY_DEFAULT=test-api-key-for-testing
DEBUG=false

# 测试环境日志配置
LOG_LEVEL=INFO

# 测试环境禅道配置 可选值：beta | preview | online
ZENTAO_ENV=beta
ZENTAO_TIMEOUT=20

# 测试环境容器配置
CONTAINER_NAME=zentao-backend-test
NETWORK_NAME=zentao-test-network
VOLUME_PREFIX=zentao-backend-test

# Docker Compose 资源配置
MEMORY_LIMIT=512M
CPU_LIMIT=0.25
HEALTH_START_PERIOD=30s
REPLICAS=1
