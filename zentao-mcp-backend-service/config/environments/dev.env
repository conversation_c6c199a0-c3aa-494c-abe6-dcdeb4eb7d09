# 开发环境特定配置
# 只包含与通用配置不同的项目

# 环境标识
APP_VERSION=dev
ENVIRONMENT=development

# 开发环境数据库
DATABASE_URL=sqlite:///./data/zentao_mcp_dev.db
DATABASE_ECHO=true

# 开发环境服务配置
WORKERS=1
RELOAD=true

# 开发环境安全配置
ADMIN_API_KEY_DEFAULT=dev-api-key-for-testing
DEBUG=true

# 开发环境日志配置
LOG_LEVEL=DEBUG
LOG_FORMAT=detailed

# 开发环境禅道配置 可选值：beta | preview | online
ZENTAO_ENV=beta

# 开发环境工具配置
ENABLE_PROFILER=true

# 开发环境容器配置
CONTAINER_NAME=zentao-backend-dev
NETWORK_NAME=zentao-dev-network
