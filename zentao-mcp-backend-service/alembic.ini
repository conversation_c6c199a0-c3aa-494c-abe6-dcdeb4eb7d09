[alembic]
script_location = migrations
prepend_sys_path = .
version_path_separator = os
# 使用环境变量配置数据库URL，支持不同环境使用不同数据库
# 默认值为开发环境数据库，可通过 DATABASE_URL 环境变量覆盖
# 注意：alembic需要在env.py中处理环境变量，这里先使用占位符
sqlalchemy.url = driver://user:pass@localhost/dbname

[post_write_hooks]

[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handlers = console
qualname =

[logger_sqlalchemy]
level = WARN
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S