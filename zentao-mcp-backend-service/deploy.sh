#!/bin/bash
# ============================================================================
# Zentao MCP Backend Service 部署脚本
# 支持 dev/test/prod 三种环境的独立部署
# ============================================================================

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
SERVICE_DIR="$SCRIPT_DIR"
CONFIG_DIR="$SERVICE_DIR/config"

# 加载公共函数库
if [[ -f "$PROJECT_ROOT/scripts/common.sh" ]]; then
    source "$PROJECT_ROOT/scripts/common.sh"
else
    echo "错误: 找不到公共函数库 $PROJECT_ROOT/scripts/common.sh"
    exit 1
fi

# 显示帮助信息
show_help() {
    cat << 'EOF'
Zentao MCP Backend Service 部署脚本

用法: ./deploy.sh [选项] <环境> [动作]

环境:
  dev       开发环境 (默认) - SQLite数据库，开发配置
  test      测试环境 - SQLite数据库，测试配置
  prod      生产环境 - SQLite数据库，生产配置

动作:
  deploy    构建并部署服务 (默认)
  build     仅构建镜像
  start     启动已存在的服务
  stop      停止服务
  restart   重启服务
  logs      查看服务日志
  status    查看服务状态
  clean     清理环境（停止并删除容器、镜像、卷）
  health    检查服务健康状态
  init      初始化数据库和管理员账户

选项:
  -e, --engine ENGINE    指定容器引擎 (docker|podman)
  -f, --force           强制重新构建镜像
  -v, --verbose         详细输出模式
  --no-cache            构建时不使用缓存
  --pull                构建前拉取最新基础镜像
  --quiet               静默模式，不显示进度指示器
  -h, --help            显示帮助信息

示例:
  ./deploy.sh dev                    # 部署开发环境
  ./deploy.sh prod --engine=podman   # 使用Podman部署生产环境
  ./deploy.sh test build --force     # 强制重新构建测试环境
  ./deploy.sh dev logs               # 查看开发环境日志
  ./deploy.sh prod clean             # 清理生产环境

环境说明:
  - dev:  SQLite数据库，热重载，详细日志，开发工具启用
  - test: SQLite数据库，生产模式，中等日志，测试配置
  - prod: SQLite数据库，生产优化，错误日志，安全配置
EOF
}

# 验证项目环境
validate_project_environment() {
    local env=$1
    
    log_info "验证后端项目环境..."
    
    # 检查项目目录
    if [[ ! -f "$SERVICE_DIR/pyproject.toml" ]]; then
        log_error "pyproject.toml 不存在: $SERVICE_DIR/pyproject.toml"
        return 1
    fi
    
    # 检查配置目录
    if [[ ! -d "$CONFIG_DIR" ]]; then
        log_error "配置目录不存在: $CONFIG_DIR"
        return 1
    fi
    
    # 检查环境配置文件
    local env_file="$CONFIG_DIR/environments/${env}.env"
    if [[ ! -f "$env_file" ]]; then
        log_error "环境配置文件不存在: $env_file"
        return 1
    fi
    
    # 对于test/prod环境，检查Docker配置文件
    if [[ "$env" != "dev" ]]; then
        # 检查Compose配置文件
        local compose_file="$CONFIG_DIR/docker-compose.yml"
        if [[ ! -f "$compose_file" ]]; then
            log_error "Compose配置文件不存在: $compose_file"
            return 1
        fi

        # 检查Dockerfile
        local dockerfile="$CONFIG_DIR/Dockerfile"
        if [[ ! -f "$dockerfile" ]]; then
            log_error "Dockerfile不存在: $dockerfile"
            return 1
        fi
    fi
    
    log_success "项目环境验证完成"
}

# 获取配置文件路径
get_config_paths() {
    local env=$1
    ENV_FILE="$CONFIG_DIR/environments/${env}.env"
    COMPOSE_FILE="$CONFIG_DIR/docker-compose.yml"
    DOCKERFILE="$CONFIG_DIR/Dockerfile"
}

# 构建镜像
build_images() {
    local env=$1
    local build_args=""

    if [[ "$NO_CACHE" == "true" ]]; then
        build_args="$build_args --no-cache"
    fi

    if [[ "$PULL_LATEST" == "true" ]]; then
        build_args="$build_args --pull"
    fi

    cd "$SERVICE_DIR"

    # 自动同步依赖文件
    log_info "同步依赖文件..."
    if [[ -f "scripts/sync-requirements.py" ]]; then
        if python3 scripts/sync-requirements.py check; then
            log_info "依赖文件已同步"
        else
            log_info "正在同步 pyproject.toml 到 requirements.txt..."
            if python3 scripts/sync-requirements.py; then
                log_success "依赖文件同步完成"
            else
                log_error "依赖文件同步失败"
                return 1
            fi
        fi
    else
        log_warning "未找到依赖同步脚本，跳过同步"
    fi

    # 添加构建参数支持镜像源（全面优化）
    local build_args_extra=""
    if [[ -f "$PROJECT_ROOT/config/mirrors.env" ]]; then
        source "$PROJECT_ROOT/config/mirrors.env"
        build_args_extra="--build-arg PIP_INDEX_URL=${PIP_INDEX_URL:-https://pypi.tuna.tsinghua.edu.cn/simple}"
        build_args_extra="$build_args_extra --build-arg PIP_TRUSTED_HOST=${PIP_TRUSTED_HOST:-pypi.tuna.tsinghua.edu.cn}"
        build_args_extra="$build_args_extra --build-arg APT_MIRROR=${APT_MIRROR:-mirrors.tuna.tsinghua.edu.cn}"

        # 构建缓存优化（仅在支持的情况下启用）
        # 注意：--cache-from/--cache-to 参数只能用于 docker buildx build，不能用于 docker compose build
        if [[ "${USE_BUILD_CACHE:-false}" == "true" && "$CONTAINER_ENGINE" == "docker" ]]; then
            # 检查是否支持buildx
            if docker buildx version &> /dev/null; then
                log_info "检测到Docker Buildx，但docker compose build不支持buildx缓存参数"
                log_info "如需使用构建缓存，请考虑使用docker buildx build命令"
            else
                log_warning "Docker Buildx不可用，跳过构建缓存优化"
            fi
        fi
    fi

    local build_cmd="$COMPOSE_CMD -f '$COMPOSE_FILE' --env-file '$ENV_FILE' build $build_args $build_args_extra"
    execute_with_progress "构建 $env 环境镜像" "$build_cmd" "always"
}

# 启动开发服务器（直接运行源码）
start_dev_server() {
    local env=$1

    log_info "启动 $env 环境开发服务器..."

    cd "$SERVICE_DIR"

    # 检查虚拟环境
    if [[ ! -f ".venv/pyvenv.cfg" ]]; then
        log_info "创建虚拟环境..."
        uv venv
    fi

    # 同步依赖
    log_info "同步开发依赖..."
    uv sync --dev

    # 启动开发服务器
    log_info "启动开发服务器 (支持热重载)..."
    log_info "访问地址: http://localhost:8000"
    log_info "API文档: http://localhost:8000/docs"
    log_info "按 Ctrl+C 停止服务器"

    uv run uvicorn main:app --host 0.0.0.0 --port 8000 --reload
}

# 检查并停止已运行的容器
check_and_stop_existing_containers() {
    local env=$1
    local container_name="${CONTAINER_NAME:-zentao-backend-${env}}"

    log_info "检查已运行的容器..."

    # 检查容器是否存在并运行
    if [[ "$CONTAINER_ENGINE" == "docker" ]]; then
        if docker ps -q -f name="$container_name" | grep -q .; then
            log_warning "发现运行中的容器: $container_name"
            log_info "停止现有容器..."
            docker stop "$container_name" || true
            docker rm "$container_name" || true
        elif docker ps -a -q -f name="$container_name" | grep -q .; then
            log_warning "发现已停止的容器: $container_name"
            log_info "删除现有容器..."
            docker rm "$container_name" || true
        fi
    else
        if podman ps -q -f name="$container_name" | grep -q .; then
            log_warning "发现运行中的容器: $container_name"
            log_info "停止现有容器..."
            podman stop "$container_name" || true
            podman rm "$container_name" || true
        elif podman ps -a -q -f name="$container_name" | grep -q .; then
            log_warning "发现已停止的容器: $container_name"
            log_info "删除现有容器..."
            podman rm "$container_name" || true
        fi
    fi

    log_success "容器检查完成"
}

# 部署服务（支持热部署）
deploy_services() {
    local env=$1

    cd "$SERVICE_DIR"

    # 检查并停止已运行的容器
    check_and_stop_existing_containers "$env"

    # 确保网络存在
    local network_name
    network_name=$(grep "^NETWORK_NAME=" "$ENV_FILE" | cut -d'=' -f2 | tr -d '"' | tr -d "'")
    network_name=${network_name:-"zentao-${env}-network"}
    ensure_network "$network_name"

    # 启动服务
    local deploy_cmd="$COMPOSE_CMD -f '$COMPOSE_FILE' --env-file '$ENV_FILE' up -d"
    execute_with_progress "部署 $env 环境服务" "$deploy_cmd" "always"

    # 等待服务就绪
    wait_for_service "http://localhost:8000/health"
}

# 启动服务
start_services() {
    local env=$1

    log_info "启动 $env 环境服务..."

    cd "$SERVICE_DIR"
    $COMPOSE_CMD -f "$COMPOSE_FILE" --env-file "$ENV_FILE" start

    log_success "服务启动完成"
}

# 停止服务
stop_services() {
    local env=$1

    log_info "停止 $env 环境服务..."

    cd "$SERVICE_DIR"
    $COMPOSE_CMD -f "$COMPOSE_FILE" --env-file "$ENV_FILE" stop

    log_success "服务停止完成"
}

# 重启服务
restart_services() {
    local env=$1

    log_info "重启 $env 环境服务..."

    cd "$SERVICE_DIR"
    $COMPOSE_CMD -f "$COMPOSE_FILE" --env-file "$ENV_FILE" restart

    log_success "服务重启完成"
}

# 查看服务状态
show_status() {
    local env=$1

    log_info "查看 $env 环境服务状态..."

    cd "$SERVICE_DIR"
    $COMPOSE_CMD -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps
}

# 查看日志
show_logs() {
    local env=$1

    log_info "查看 $env 环境日志..."

    cd "$SERVICE_DIR"
    $COMPOSE_CMD -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs -f
}

# 健康检查
health_check() {
    local env=$1
    health_check "http://localhost:8000/health" "后端服务"
}

# 清理环境
clean_environment() {
    local env=$1

    log_warning "清理 $env 环境（这将删除所有数据）..."

    # 确认操作
    if [[ "$FORCE_REBUILD" != "true" ]]; then
        echo -n "确认清理 $env 环境？这将删除所有容器、镜像和数据卷 [y/N]: "
        read -r confirm
        if [[ "$confirm" != "y" && "$confirm" != "Y" ]]; then
            log_info "取消清理操作"
            return 0
        fi
    fi

    cd "$SERVICE_DIR"
    log_info "停止并删除服务..."
    $COMPOSE_CMD -f "$COMPOSE_FILE" --env-file "$ENV_FILE" down --volumes || true

    # 删除镜像
    local image_name="zentao-mcp-backend-${env}"
    if [[ "$CONTAINER_ENGINE" == "docker" ]]; then
        docker rmi "$image_name" 2>/dev/null || true
    else
        podman rmi "$image_name" 2>/dev/null || true
    fi

    log_success "环境清理完成"
}

# 初始化数据库和管理员
init_database() {
    local env=$1
    
    log_info "初始化 $env 环境数据库..."
    
    cd "$SERVICE_DIR"
    
    # 检查服务是否运行
    if ! curl -f -s http://localhost:8000/health &> /dev/null; then
        log_error "后端服务未运行，请先启动服务"
        return 1
    fi
    
    # 运行初始化脚本 - 使用统一管理脚本
    if [[ -f "manage.py" ]]; then
        log_info "执行系统初始化..."
        uv run python manage.py init
    else
        log_error "未找到管理脚本 manage.py"
        return 1
    fi
    
    log_success "数据库初始化完成"
}

# 主函数
main() {
    # 优先检查help参数，避免错误处理机制干扰
    for arg in "$@"; do
        if [[ "$arg" == "-h" || "$arg" == "--help" ]]; then
            show_help
            exit 0
        fi
    done

    log_info "Zentao MCP Backend Service 部署脚本启动"

    # 初始化公共环境
    init_common_environment

    # 解析命令行参数
    local remaining_args=()
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--engine)
                CONTAINER_ENGINE="$2"
                shift 2
                ;;
            --engine=*)
                CONTAINER_ENGINE="${1#*=}"
                shift
                ;;
            -f|--force)
                FORCE_REBUILD=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            --no-cache)
                NO_CACHE=true
                shift
                ;;
            --pull)
                PULL_LATEST=true
                shift
                ;;
            dev|test|prod)
                ENVIRONMENT="$1"
                shift
                ;;
            deploy|build|start|stop|restart|logs|status|clean|test)
                ACTION="$1"
                shift
                ;;
            *)
                remaining_args+=("$1")
                shift
                ;;
        esac
    done

    # 处理剩余参数
    if [[ ${#remaining_args[@]} -gt 0 ]]; then
        for arg in "${remaining_args[@]}"; do
            case $arg in
                init)
                    ACTION="init"
                    ;;
                --quiet)
                    SHOW_PROGRESS=false
                    ;;
                *)
                    log_error "未识别的参数: $arg"
                    log_error "请使用 --help 查看正确的使用方法"
                    show_help
                    exit 1
                    ;;
            esac
        done
    fi

    # 验证环境参数
    validate_environment "$ENVIRONMENT" || exit 1

    # 根据环境和动作决定需要检查的工具
    if [[ "$ENVIRONMENT" == "dev" ]] || [[ "$ACTION" == "init" ]]; then
        # 开发环境或初始化操作需要本地Python环境
        check_python_environment || exit 1
    fi

    # 对于容器化部署，检测容器引擎
    if [[ "$ENVIRONMENT" != "dev" ]] || [[ "$ACTION" =~ ^(deploy|build|start|stop|restart|logs|status|clean)$ ]]; then
        detect_container_engine || exit 1
        setup_compose_command || exit 1
    fi

    # 验证项目环境
    validate_project_environment "$ENVIRONMENT" || exit 1

    # 获取配置文件路径
    get_config_paths "$ENVIRONMENT"

    log_info "使用配置:"
    log_info "  环境: $ENVIRONMENT"
    log_info "  容器引擎: $CONTAINER_ENGINE"
    log_info "  动作: $ACTION"
    log_info "  配置文件: $ENV_FILE"
    log_info "  Compose文件: $COMPOSE_FILE"

    # 执行对应的动作
    case $ACTION in
        deploy)
            if [[ "$ENVIRONMENT" == "dev" ]]; then
                start_dev_server "$ENVIRONMENT"
            else
                build_images "$ENVIRONMENT"
                deploy_services "$ENVIRONMENT"
                show_status "$ENVIRONMENT"
            fi
            ;;
        build)
            if [[ "$ENVIRONMENT" == "dev" ]]; then
                log_info "开发环境不需要构建镜像，直接同步依赖..."
                cd "$SERVICE_DIR"
                uv sync --dev
                log_success "开发环境依赖同步完成"
            else
                build_images "$ENVIRONMENT"
            fi
            ;;
        start)
            start_services "$ENVIRONMENT"
            ;;
        stop)
            stop_services "$ENVIRONMENT"
            ;;
        restart)
            restart_services "$ENVIRONMENT"
            ;;
        status)
            show_status "$ENVIRONMENT"
            ;;
        logs)
            show_logs "$ENVIRONMENT"
            ;;
        health)
            health_check "$ENVIRONMENT"
            ;;
        clean)
            clean_environment "$ENVIRONMENT"
            ;;
        init)
            init_database "$ENVIRONMENT"
            ;;
        *)
            log_error "未知动作: $ACTION"
            show_help
            exit 1
            ;;
    esac

    log_success "操作完成!"
}

# 执行主函数
main "$@"
