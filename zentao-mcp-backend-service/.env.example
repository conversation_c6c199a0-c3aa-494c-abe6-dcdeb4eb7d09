# Zentao MCP Backend Service - 通用配置模板
# 复制此文件为 .env 并根据需要修改配置
# 此文件包含所有环境共享的基础配置
# 环境特定配置在 config/environments/*.env 中定义

# ============================================================================
# 应用基础配置 - 所有环境通用
# ============================================================================
APP_NAME=zentao-mcp-backend
APP_VERSION=online
ENVIRONMENT=production
HOST=0.0.0.0
PORT=8000
WORKERS=4
RELOAD=false

# ============================================================================
# 数据库配置 - 建议不同环境使用不同数据库
# ============================================================================
DATABASE_URL=sqlite:///./data/zentao_mcp.db
DATABASE_ECHO=false

# ============================================================================
# 安全配置 - 生产环境必须修改
# ============================================================================
ADMIN_API_KEY_DEFAULT=CHANGE_ME_IN_PRODUCTION
DEBUG=false
CORS_ORIGINS=https://yourdomain.com

# ============================================================================
# 管理员配置 - 首次初始化使用
# ============================================================================
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
ADMIN_EMAIL=<EMAIL>

# ============================================================================
# 日志配置 - 根据环境调整级别
# ============================================================================
LOG_LEVEL=WARNING
LOG_FILE=logs/zentao_mcp.log
LOG_FORMAT=json

# ============================================================================
# 禅道API配置 - 根据实际环境选择
# ============================================================================
# 方式一：通过环境类型自动映射（推荐）
# 可选值：beta | preview | online
ZENTAO_ENV=online

# 方式二：显式指定完整URL（设置此项将覆盖上面的环境映射）
# ZENTAO_BASE_URL=http://your-zentao-server.com

# API超时设置（秒）
ZENTAO_TIMEOUT=10

# ============================================================================
# 开发工具配置 - 生产环境建议关闭
# ============================================================================
ENABLE_DOCS=false
ENABLE_PROFILER=false

# ============================================================================
# 容器配置 - Docker/Podman 部署使用
# ============================================================================
CONTAINER_NAME=zentao-backend
NETWORK_NAME=zentao-network
VOLUME_PREFIX=zentao-backend

# Docker Compose 资源配置
MEMORY_LIMIT=1G
CPU_LIMIT=1.0
HEALTH_START_PERIOD=60s
REPLICAS=2
