"""
Configuration settings for Zentao MCP Backend Service
支持分层配置：通用配置(.env) + 环境特定配置(config/environments/*.env)
"""
from pydantic_settings import BaseSettings
from pydantic import ConfigDict, model_validator
from typing import Optional, List
import os
from pathlib import Path


class Settings(BaseSettings):
    """Application settings - 支持分层配置"""

    # 应用基础配置
    app_name: str = "zentao-mcp-backend"
    app_version: str = "dev"
    environment: str = "development"
    host: str = "0.0.0.0"
    port: int = 8000
    workers: int = 1
    reload: bool = False

    # 数据库配置
    database_url: str = "sqlite:///./data/zentao_mcp.db"
    database_echo: bool = False

    # 安全配置
    admin_api_key_default: str = "dev-api-key-for-testing"
    debug: bool = False
    cors_origins: str = "http://localhost:3000,http://localhost:5173"

    # 管理员配置
    admin_username: str = "admin"
    admin_password: str = "admin123"
    admin_email: str = "<EMAIL>"

    # 日志配置
    log_level: str = "INFO"
    log_file: str = "logs/zentao_mcp.log"
    log_format: str = "json"

    # 禅道API配置
    zentao_env: Optional[str] = None  # 可选: beta | preview | online
    zentao_base_url: Optional[str] = None
    zentao_timeout: int = 30

    # 开发工具配置
    enable_docs: bool = True
    enable_profiler: bool = False

    # 容器配置
    container_name: str = "zentao-backend"
    network_name: str = "zentao-network"
    volume_prefix: Optional[str] = None

    @model_validator(mode="after")
    def _apply_zentao_env_mapping(self) -> "Settings":
        """
        启动时根据 ZENTAO_ENV 自动映射 zentao_base_url。
        若 .env 显式提供 ZENTAO_BASE_URL，则优先生效。
        """
        if (not self.zentao_base_url) and self.zentao_env:
            env = str(self.zentao_env).strip().lower()
            mapping = {
                "beta": "http://newzentao-api.beta1.fn/",
                "preview": "http://newzentao-api.idc1.fn/",
                "online": "http://newzentao-api.idc1.fn/",
            }
            mapped = mapping.get(env)
            if mapped:
                self.zentao_base_url = mapped
        return self
    
    model_config = ConfigDict(
        env_file_encoding="utf-8",
        extra="ignore",  # 忽略额外的环境变量
        case_sensitive=False
    )


def get_env_files() -> List[str]:
    """
    获取配置文件列表，支持分层配置
    优先级：环境特定配置 > 通用配置
    """
    root_path = Path(__file__).parent.parent.parent
    env_files = []

    # 1. 通用配置文件（优先级低）
    base_env = root_path / ".env"
    if base_env.exists():
        env_files.append(str(base_env))

    # 2. 环境特定配置文件（优先级高）
    environment = os.getenv("ENVIRONMENT", "development")
    env_mapping = {
        "development": "dev.env",
        "testing": "test.env",
        "production": "prod.env"
    }

    env_file = env_mapping.get(environment, "dev.env")
    env_path = root_path / "config" / "environments" / env_file
    if env_path.exists():
        env_files.append(str(env_path))

    return env_files


# 创建设置实例，支持多文件配置
settings = Settings(_env_file=get_env_files())