#!/usr/bin/env python3
"""
Zentao MCP Backend Service 调试运行脚本
支持直接源码运行不同环境配置
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path


def setup_environment(env_name):
    """设置环境变量"""
    print(f"🔧 设置 {env_name} 环境...")

    # 设置环境标识（必须在导入配置之前）
    os.environ['ENVIRONMENT'] = {
        'dev': 'development',
        'test': 'testing',
        'prod': 'production'
    }.get(env_name, 'development')

    print(f"   ENVIRONMENT = {os.environ['ENVIRONMENT']}")


def show_environment_config():
    """显示环境配置"""
    try:
        # 强制重新加载配置模块
        import importlib
        import sys
        if 'app.core.config' in sys.modules:
            importlib.reload(sys.modules['app.core.config'])

        from app.core.config import settings
        print(f"   数据库: {settings.database_url}")
        print(f"   端口: {settings.port}")
        print(f"   DEBUG: {settings.debug}")
        print(f"   默认API Key: {settings.admin_api_key_default}")
        print(f"   禅道环境: {settings.zentao_env}")
    except Exception as e:
        print(f"   ⚠️  配置加载失败: {e}")


def check_dependencies():
    """检查依赖"""
    print("📦 检查依赖...")
    
    try:
        import fastapi
        import uvicorn
        import sqlalchemy
        print("   ✅ 核心依赖已安装")
        return True
    except ImportError as e:
        print(f"   ❌ 缺少依赖: {e}")
        print("   💡 请运行: pip install -r requirements.txt")
        return False


def init_database(env_name):
    """初始化数据库"""
    print(f"🗄️  初始化 {env_name} 环境数据库...")
    
    try:
        from app.core.init import initialize_system
        success = initialize_system()
        if success:
            print("   ✅ 数据库初始化成功")
        else:
            print("   ❌ 数据库初始化失败")
        return success
    except Exception as e:
        print(f"   ❌ 数据库初始化异常: {e}")
        return False


def run_server(env_name, port=None, host="0.0.0.0"):
    """运行服务器"""
    print(f"🚀 启动 {env_name} 环境服务器...")
    
    try:
        from app.core.config import settings
        
        # 使用配置的端口或指定端口
        server_port = port or settings.port
        server_host = host
        
        print(f"   监听地址: {server_host}:{server_port}")
        print(f"   访问地址: http://{server_host}:{server_port}")
        print(f"   文档地址: http://{server_host}:{server_port}/docs")
        print()
        print("🔥 服务器启动中... (Ctrl+C 停止)")
        print("=" * 50)
        
        # 启动服务器
        import uvicorn
        uvicorn.run(
            "main:app",
            host=server_host,
            port=server_port,
            reload=settings.reload,
            log_level=settings.log_level.lower()
        )
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")


def show_status(env_name):
    """显示状态信息"""
    print(f"📊 {env_name} 环境状态:")
    
    try:
        from app.core.init import get_system_status
        from app.core.config import settings
        
        status = get_system_status()
        
        print(f"   环境: {settings.environment}")
        print(f"   数据库: {settings.database_url}")
        print(f"   初始化状态: {'✅ 已完成' if status['initialized'] else '❌ 未完成'}")
        print(f"   管理员用户数: {status.get('admin_users', 0)}")
        print(f"   API Key数量: {status.get('api_keys', 0)}")
        
        if 'error' in status:
            print(f"   错误: {status['error']}")
            
    except Exception as e:
        print(f"   ❌ 状态获取失败: {e}")


def main():
    parser = argparse.ArgumentParser(description="Zentao MCP Backend Service 调试运行脚本")
    parser.add_argument('env', choices=['dev', 'test', 'prod'], help='运行环境')
    parser.add_argument('--action', choices=['run', 'init', 'status'], default='run', 
                       help='执行动作 (默认: run)')
    parser.add_argument('--port', type=int, help='指定端口号')
    parser.add_argument('--host', default='0.0.0.0', help='监听主机地址')
    parser.add_argument('--no-init', action='store_true', help='跳过数据库初始化')
    
    args = parser.parse_args()
    
    print("🎯 Zentao MCP Backend Service 调试运行器")
    print("=" * 50)
    
    # 切换到项目目录
    project_dir = Path(__file__).parent
    os.chdir(project_dir)

    # 添加项目目录到Python路径
    if str(project_dir) not in sys.path:
        sys.path.insert(0, str(project_dir))

    print(f"📁 工作目录: {project_dir}")
    
    # 设置环境
    setup_environment(args.env)

    # 显示配置
    show_environment_config()
    print()

    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    print()
    
    # 执行动作
    if args.action == 'status':
        show_status(args.env)
        
    elif args.action == 'init':
        init_database(args.env)
        
    elif args.action == 'run':
        # 初始化数据库（除非跳过）
        if not args.no_init:
            if not init_database(args.env):
                print("❌ 数据库初始化失败，无法启动服务器")
                sys.exit(1)
            print()
        
        # 显示状态
        show_status(args.env)
        print()
        
        # 启动服务器
        run_server(args.env, args.port, args.host)


if __name__ == "__main__":
    main()
