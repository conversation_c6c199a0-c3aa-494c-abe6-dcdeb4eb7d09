#!/usr/bin/env python3
"""
Zentao MCP Backend Service - 统一管理脚本
整合了所有初始化、启动和管理功能

用法:
    python manage.py init          # 完整系统初始化
    python manage.py init-db       # 仅数据库初始化
    python manage.py init-admin    # 仅管理员初始化
    python manage.py start         # 启动服务
    python manage.py status        # 查看系统状态
    python manage.py reset         # 重置系统
    python manage.py --help        # 显示帮助
"""

import sys
import os
import logging
import argparse
import subprocess
import asyncio
from pathlib import Path
from typing import Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.config import settings
from app.core.database import engine, Base, get_db
from app.core.init import initialize_system, get_system_status
from app.services.user_management_service import UserManagementService
from app.models import UserType
from sqlalchemy import text

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ZentaoManager:
    """Zentao MCP Backend Service 管理器"""
    
    def __init__(self):
        self.project_root = project_root
        
    def print_banner(self, title: str):
        """打印标题横幅"""
        print("=" * 60)
        print(f"Zentao MCP Backend Service - {title}")
        print("=" * 60)
        
    def print_config_info(self):
        """显示配置信息"""
        print(f"\n📋 当前配置:")
        print(f"   环境: {settings.environment}")
        print(f"   数据库: {settings.database_url}")
        print(f"   管理员用户名: {settings.admin_username}")
        print(f"   管理员邮箱: {settings.admin_email}")
        print(f"   调试模式: {settings.debug}")
        
    def create_data_directory(self):
        """创建数据目录"""
        data_dir = self.project_root / "data"
        if not data_dir.exists():
            data_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"✅ 创建数据目录: {data_dir.absolute()}")
        else:
            logger.info(f"✅ 数据目录已存在: {data_dir.absolute()}")
            
    def check_database_connection(self) -> bool:
        """检查数据库连接"""
        try:
            with engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                logger.info("✅ 数据库连接成功")
                return True
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            return False
            
    def create_tables(self) -> bool:
        """创建数据库表"""
        try:
            Base.metadata.create_all(bind=engine)
            logger.info("✅ 数据库表创建成功")
            
            # 显示创建的表
            with engine.connect() as conn:
                result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
                tables = [row[0] for row in result]
                if tables:
                    logger.info(f"📋 已创建的表: {', '.join(tables)}")
                else:
                    logger.info("📋 暂无数据表")
                    
        except Exception as e:
            logger.error(f"❌ 创建数据库表失败: {e}")
            return False
        return True
        
    def init_database(self) -> bool:
        """初始化数据库"""
        self.print_banner("数据库初始化")
        logger.info("🚀 开始初始化数据库...")
        
        # 1. 创建数据目录
        self.create_data_directory()
        
        # 2. 检查数据库连接
        if not self.check_database_connection():
            logger.error("❌ 数据库初始化失败：无法连接数据库")
            return False
        
        # 3. 创建数据库表
        if not self.create_tables():
            logger.error("❌ 数据库初始化失败：无法创建表")
            return False
        
        logger.info("🎉 数据库初始化完成！")
        return True
        
    def init_admin(self) -> bool:
        """初始化管理员用户"""
        self.print_banner("管理员初始化")
        logger.info("👤 初始化管理员用户...")
        
        db = next(get_db())
        user_service = UserManagementService(db)
        
        try:
            # 检查是否已有管理员
            existing_admins = user_service.get_users(user_type=UserType.ADMIN)
            
            if existing_admins:
                logger.info(f"✅ 已存在 {len(existing_admins)} 个管理员:")
                for admin in existing_admins:
                    logger.info(f"   - {admin.username} ({admin.email})")
                return True
            
            # 创建默认管理员
            admin_data = {
                "username": settings.admin_username,
                "email": settings.admin_email,
                "password": settings.admin_password,
                "user_type": UserType.ADMIN,
                "is_active": True
            }
            
            admin_user = user_service.create_user(**admin_data)
            logger.info(f"✅ 管理员用户创建成功: {admin_user.username}")
            logger.info(f"   邮箱: {admin_user.email}")
            logger.info("⚠️  请及时修改默认密码！")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 管理员初始化失败: {e}")
            return False
        finally:
            db.close()
            
    def init_system(self) -> bool:
        """完整系统初始化"""
        self.print_banner("系统初始化")
        self.print_config_info()
        
        # 检查环境变量
        env_file = self.project_root / ".env"
        if not env_file.exists():
            print(f"\n⚠️  未找到 .env 文件，将使用默认配置")
            print(f"   建议复制 .env.example 到 .env 并修改配置")
        
        print(f"\n🚀 开始系统初始化...")
        
        # 执行初始化
        if initialize_system():
            print(f"✅ 系统初始化完成！")
            
            # 显示系统状态
            status = get_system_status()
            print(f"\n📊 系统状态:")
            print(f"   初始化状态: {'✅ 已完成' if status['initialized'] else '❌ 未完成'}")
            print(f"   管理员用户数: {status.get('admin_users', 0)}")
            print(f"   API Key数量: {status.get('api_keys', 0)}")
            
            # 显示登录信息
            print(f"\n🔑 管理员登录信息:")
            print(f"   Web管理界面: http://localhost:3000")
            print(f"   用户名: {settings.admin_username}")
            print(f"   密码: {settings.admin_password}")
            print(f"   邮箱: {settings.admin_email}")
            
            print(f"\n⚠️  安全提醒:")
            print(f"   1. 请及时登录管理后台修改默认密码")
            print(f"   2. 生产环境请修改 .env 文件中的所有默认配置")
            print(f"   3. 确保数据库文件的访问权限设置正确")
            
            print(f"\n🎉 系统已准备就绪！")
            return True
        else:
            print(f"❌ 系统初始化失败！")
            print(f"   请检查日志信息并解决问题后重试")
            return False
            
    def show_status(self):
        """显示系统状态"""
        self.print_banner("系统状态")
        
        try:
            # 数据库连接状态
            db_connected = self.check_database_connection()
            
            # 系统状态
            status = get_system_status()
            
            print(f"\n📊 系统状态:")
            print(f"   数据库连接: {'✅ 正常' if db_connected else '❌ 异常'}")
            print(f"   初始化状态: {'✅ 已完成' if status['initialized'] else '❌ 未完成'}")
            print(f"   管理员用户数: {status.get('admin_users', 0)}")
            print(f"   API Key数量: {status.get('api_keys', 0)}")
            
            # 配置信息
            self.print_config_info()
            
            # 服务状态检查
            print(f"\n🌐 服务状态:")
            try:
                import requests
                response = requests.get("http://localhost:8000/health", timeout=5)
                if response.status_code == 200:
                    print(f"   HTTP服务: ✅ 运行中 (http://localhost:8000)")
                    print(f"   API文档: http://localhost:8000/docs")
                else:
                    print(f"   HTTP服务: ⚠️  响应异常 (状态码: {response.status_code})")
            except:
                print(f"   HTTP服务: ❌ 未运行或无法访问")
                
        except Exception as e:
            logger.error(f"❌ 获取系统状态失败: {e}")
            
    def get_python_executable(self) -> str:
        """获取Python可执行文件路径"""
        venv_python = self.project_root / ".venv" / "bin" / "python"
        if venv_python.exists():
            logger.info(f"使用虚拟环境中的Python: {venv_python}")
            return str(venv_python)
        
        # 检查uv环境
        try:
            result = subprocess.run(["uv", "run", "python", "--version"], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                logger.info("使用uv管理的Python环境")
                return "uv run python"
        except:
            pass
            
        logger.info(f"使用系统Python: {sys.executable}")
        return sys.executable
        
    def start_server(self):
        """启动服务器"""
        self.print_banner("启动服务")
        
        python_cmd = self.get_python_executable()
        
        # 构建启动命令
        if python_cmd == "uv run python":
            cmd = ["uv", "run", "python", "-m", "uvicorn", "main:app"]
        else:
            cmd = [python_cmd, "-m", "uvicorn", "main:app"]
            
        # 添加启动参数
        cmd.extend([
            "--host", settings.host,
            "--port", str(settings.port),
        ])
        
        if settings.reload:
            cmd.append("--reload")
            
        if settings.workers > 1:
            cmd.extend(["--workers", str(settings.workers)])
            
        logger.info(f"🚀 启动命令: {' '.join(cmd)}")
        logger.info(f"📡 服务地址: http://{settings.host}:{settings.port}")
        logger.info(f"📚 API文档: http://{settings.host}:{settings.port}/docs")
        
        try:
            subprocess.run(cmd, cwd=self.project_root)
        except KeyboardInterrupt:
            logger.info("\n👋 服务已停止")
        except Exception as e:
            logger.error(f"❌ 启动失败: {e}")
            
    def reset_system(self):
        """重置系统"""
        self.print_banner("系统重置")
        
        print("⚠️  此操作将删除所有数据，包括:")
        print("   - 数据库文件")
        print("   - 日志文件")
        print("   - 用户数据")
        
        confirm = input("\n确认重置系统？(输入 'yes' 确认): ")
        if confirm.lower() != 'yes':
            print("❌ 操作已取消")
            return
            
        try:
            # 删除数据库文件
            if "sqlite:///" in settings.database_url:
                db_path = settings.database_url.replace("sqlite:///", "")
                if db_path.startswith("./"):
                    db_path = self.project_root / db_path[2:]
                else:
                    db_path = Path(db_path)
                    
                if db_path.exists():
                    db_path.unlink()
                    logger.info(f"✅ 已删除数据库文件: {db_path}")
                    
            # 清理日志文件
            logs_dir = self.project_root / "logs"
            if logs_dir.exists():
                for log_file in logs_dir.glob("*.log"):
                    log_file.unlink()
                    logger.info(f"✅ 已删除日志文件: {log_file}")
                    
            print("🎉 系统重置完成！")
            print("💡 提示: 运行 'python manage.py init' 重新初始化系统")
            
        except Exception as e:
            logger.error(f"❌ 系统重置失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Zentao MCP Backend Service 统一管理脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python manage.py init          # 完整系统初始化
  python manage.py init-db       # 仅数据库初始化
  python manage.py init-admin    # 仅管理员初始化
  python manage.py start         # 启动服务
  python manage.py status        # 查看系统状态
  python manage.py reset         # 重置系统
        """
    )
    
    parser.add_argument(
        'command',
        choices=['init', 'init-db', 'init-admin', 'start', 'status', 'reset'],
        help='要执行的命令'
    )
    
    args = parser.parse_args()
    
    manager = ZentaoManager()
    
    try:
        if args.command == 'init':
            success = manager.init_system()
            sys.exit(0 if success else 1)
        elif args.command == 'init-db':
            success = manager.init_database()
            sys.exit(0 if success else 1)
        elif args.command == 'init-admin':
            success = manager.init_admin()
            sys.exit(0 if success else 1)
        elif args.command == 'start':
            manager.start_server()
        elif args.command == 'status':
            manager.show_status()
        elif args.command == 'reset':
            manager.reset_system()
            
    except KeyboardInterrupt:
        print("\n👋 操作已中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
