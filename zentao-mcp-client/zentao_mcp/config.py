"""
客户端配置管理模块
"""

import os
import configparser
from pathlib import Path
from typing import Optional


class ClientConfig:
    """客户端配置管理"""
    
    def __init__(self, cli_api_key: Optional[str] = None, cli_backend_url: Optional[str] = None):
        self.config_dir = Path.home() / ".zentao_mcp_client"
        self.config_file = self.config_dir / "config.ini"
        self.config = configparser.ConfigParser()

        # 命令行参数
        self.cli_api_key = cli_api_key
        self.cli_backend_url = cli_backend_url

        # 确保配置目录存在
        self.config_dir.mkdir(exist_ok=True)

        # 加载现有配置
        self.load_config()
    
    def load_config(self) -> None:
        """加载配置文件"""
        if self.config_file.exists():
            try:
                self.config.read(self.config_file)
            except configparser.Error:
                # 如果配置文件损坏，创建新的配置
                self.config = configparser.ConfigParser()

        # 确保有client section
        if not self.config.has_section('client'):
            self.config.add_section('client')
    
    def save_config(self) -> None:
        """保存配置文件"""
        with open(self.config_file, 'w') as f:
            self.config.write(f)
    
    def set_backend_url(self, url: Optional[str]) -> None:
        """设置后端服务URL"""
        if not self.config.has_section('client'):
            self.config.add_section('client')
        self.config.set('client', 'backend_url', url or '')
        self.save_config()

    def set_api_key(self, api_key: Optional[str]) -> None:
        """设置API Key"""
        if not self.config.has_section('client'):
            self.config.add_section('client')
        self.config.set('client', 'api_key', api_key or '')
        self.save_config()
    
    def get_backend_url(self) -> Optional[str]:
        """获取后端服务URL - 优先级: 环境变量 > 命令行参数 > 用户目录配置"""
        # A. 环境变量优先
        env_url = os.getenv('ZENTAO_MCP_BACKEND_URL') or os.getenv('ZENTAO_MCP_BACKEND')
        if env_url:
            return env_url.rstrip('/')

        # B. 命令行参数
        if self.cli_backend_url:
            return self.cli_backend_url.rstrip('/')

        # D. 用户目录配置
        try:
            url = self.config.get('client', 'backend_url')
            if url:
                return url.rstrip('/')
        except (configparser.NoSectionError, configparser.NoOptionError):
            pass

        return None

    def get_api_key(self) -> Optional[str]:
        """获取API Key - 优先级: 环境变量 > 命令行参数 > 用户目录配置"""
        # A. 环境变量优先
        env_key = os.getenv('ZENTAO_MCP_API_KEY')
        if env_key:
            return env_key.strip()

        # B. 命令行参数
        if self.cli_api_key:
            return self.cli_api_key.strip()

        # D. 用户目录配置
        try:
            key = self.config.get('client', 'api_key')
            if key:
                return key.strip()
        except (configparser.NoSectionError, configparser.NoOptionError):
            pass

        return None
    
    def is_configured(self) -> bool:
        """检查是否已配置"""
        backend_url = self.get_backend_url()
        api_key = self.get_api_key()
        return bool(backend_url and api_key)
    
    def get_config_info(self) -> dict:
        """获取配置信息（不包含敏感信息），并标注来源"""
        # 获取当前生效的配置
        backend_url = self.get_backend_url()
        api_key = self.get_api_key()

        # 确定backend_url来源
        source_backend = "none"
        env_url = os.getenv('ZENTAO_MCP_BACKEND_URL') or os.getenv('ZENTAO_MCP_BACKEND')
        if env_url:
            source_backend = "env"
        elif self.cli_backend_url:
            source_backend = "cli"
        elif backend_url:
            source_backend = "home"

        # 确定api_key来源
        source_api_key = "none"
        env_key = os.getenv('ZENTAO_MCP_API_KEY')
        if env_key:
            source_api_key = "env"
        elif self.cli_api_key:
            source_api_key = "cli"
        elif api_key:
            source_api_key = "home"

        # API Key预览逻辑
        if api_key:
            if len(api_key) <= 3:
                api_key_preview = "***"
            elif len(api_key) <= 6:
                api_key_preview = f"{api_key[:3]}***"
            else:
                api_key_preview = f"{api_key[:2]}***{api_key[-2:]}"
        else:
            api_key_preview = 'Not configured'

        return {
            'config_file': str(self.config_file),
            'backend_url': backend_url,
            'backend_url_source': source_backend,
            'api_key_configured': bool(api_key),
            'api_key_preview': api_key_preview,
            'api_key_source': source_api_key,
        }