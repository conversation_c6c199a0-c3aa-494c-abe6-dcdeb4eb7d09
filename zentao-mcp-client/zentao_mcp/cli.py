"""
命令行接口模块
"""

import click
from zentao_mcp.config import ClientConfig
from zentao_mcp.log_viewer import logs
from zentao_mcp.proxy import start_proxy_server


@click.group()
@click.option('--api-key', help='API Key (覆盖配置文件)')
@click.option('--backend-url', help='后端服务URL (覆盖配置文件)')
@click.pass_context
def main(ctx, api_key, backend_url):
    """Zentao MCP Client - 轻量级代理客户端"""
    # 将命令行参数存储到上下文中
    ctx.ensure_object(dict)
    ctx.obj['cli_api_key'] = api_key
    ctx.obj['cli_backend_url'] = backend_url


# 添加日志管理命令组
main.add_command(logs)


@click.group()
def config():
    """配置管理命令"""
    pass


# 添加配置管理命令组
main.add_command(config)


@config.command()
@click.pass_context
def show(ctx):
    """显示所有配置来源和当前使用值"""
    import os
    from pathlib import Path

    client_config = ClientConfig(
        cli_api_key=ctx.obj.get('cli_api_key'),
        cli_backend_url=ctx.obj.get('cli_backend_url')
    )

    click.echo("=== 配置来源分析 ===")
    click.echo()

    # 1. 环境变量 (最高优先级)
    click.echo("1. 环境变量 (最高优先级):")
    env_api_key = os.getenv('ZENTAO_MCP_API_KEY')
    env_backend_url = os.getenv('ZENTAO_MCP_BACKEND_URL') or os.getenv('ZENTAO_MCP_BACKEND')

    if env_api_key:
        preview = f"{env_api_key[:2]}***{env_api_key[-2:]}" if len(env_api_key) > 4 else "***"
        click.echo(f"   ZENTAO_MCP_API_KEY = {preview} ✓")
    else:
        click.echo("   ZENTAO_MCP_API_KEY = (未设置)")

    if env_backend_url:
        click.echo(f"   ZENTAO_MCP_BACKEND_URL = {env_backend_url} ✓")
    else:
        click.echo("   ZENTAO_MCP_BACKEND_URL = (未设置)")

    click.echo()

    # 2. 命令行参数 (第二优先级)
    cli_api_key = client_config.cli_api_key
    cli_backend_url = client_config.cli_backend_url

    click.echo("2. 命令行参数 (第二优先级):")
    if cli_api_key:
        preview = f"{cli_api_key[:2]}***{cli_api_key[-2:]}" if len(cli_api_key) > 4 else "***"
        click.echo(f"   --api-key = {preview} ✓")
    else:
        click.echo("   --api-key = (未设置)")

    if cli_backend_url:
        click.echo(f"   --backend-url = {cli_backend_url} ✓")
    else:
        click.echo("   --backend-url = (未设置)")

    click.echo()

    # 3. 用户目录配置 (最低优先级)
    click.echo("3. 用户目录配置 (最低优先级):")
    config_file = client_config.config_file
    click.echo(f"   配置文件: {config_file}")

    if config_file.exists():
        try:
            local_api_key = client_config.config.get('client', 'api_key', fallback='')
            local_backend_url = client_config.config.get('client', 'backend_url', fallback='')

            if local_api_key:
                preview = f"{local_api_key[:2]}***{local_api_key[-2:]}" if len(local_api_key) > 4 else "***"
                click.echo(f"   api_key = {preview}")
            else:
                click.echo("   api_key = (未设置)")

            if local_backend_url:
                click.echo(f"   backend_url = {local_backend_url}")
            else:
                click.echo("   backend_url = (未设置)")
        except Exception as e:
            click.echo(f"   读取失败: {e}")
    else:
        click.echo("   文件不存在")

    click.echo()

    # 当前生效配置
    click.echo("=== 当前生效配置 ===")
    current_api_key = client_config.get_api_key()
    current_backend_url = client_config.get_backend_url()
    config_info = client_config.get_config_info()

    if current_api_key:
        source_map = {"env": "环境变量", "cli": "命令行参数", "home": "用户目录配置"}
        source = source_map.get(config_info['api_key_source'], "未知")
        click.echo(f"   API Key: {config_info['api_key_preview']} (来源: {source})")
    else:
        click.echo("   API Key: 未配置 ❌")

    if current_backend_url:
        source_map = {"env": "环境变量", "cli": "命令行参数", "home": "用户目录配置"}
        source = source_map.get(config_info['backend_url_source'], "未知")
        click.echo(f"   Backend URL: {current_backend_url} (来源: {source})")
    else:
        click.echo("   Backend URL: 未配置 ❌")

    # 配置状态
    if client_config.is_configured():
        click.echo("   配置状态: ✅ 完整有效")
    else:
        click.echo("   配置状态: ❌ 配置不完整")
        click.echo()
        click.echo("💡 使用 'zentao-mcp config init' 进行配置")


@config.command()
@click.pass_context
def check(ctx):
    """验证当前配置有效性"""
    import requests

    client_config = ClientConfig(
        cli_api_key=ctx.obj.get('cli_api_key'),
        cli_backend_url=ctx.obj.get('cli_backend_url')
    )

    click.echo("=== 配置验证 ===")
    click.echo()

    # 检查配置完整性
    if not client_config.is_configured():
        click.echo("❌ 配置不完整")
        click.echo("   请先使用 'zentao-mcp config init' 进行配置")
        return

    api_key = client_config.get_api_key()
    backend_url = client_config.get_backend_url()

    click.echo("✅ 配置完整性检查通过")
    click.echo(f"   API Key: {client_config.get_config_info()['api_key_preview']}")
    click.echo(f"   Backend URL: {backend_url}")
    click.echo()

    # 测试服务端连接
    click.echo("🔍 测试服务端连接...")
    try:
        # 测试健康检查端点
        health_url = f"{backend_url}/health"
        response = requests.get(health_url, timeout=5)

        if response.status_code == 200:
            click.echo("✅ 服务端连接正常")
        else:
            click.echo(f"⚠️  服务端响应异常: HTTP {response.status_code}")
    except requests.exceptions.ConnectionError:
        click.echo("❌ 无法连接到服务端")
        click.echo("   请检查服务端是否启动或URL是否正确")
        return
    except requests.exceptions.Timeout:
        click.echo("❌ 连接超时")
        return
    except Exception as e:
        click.echo(f"❌ 连接测试失败: {e}")
        return

    # 验证API Key有效性
    click.echo("🔑 验证API Key...")
    try:
        # 测试API Key验证端点
        auth_url = f"{backend_url}/api/v1/auth/verify"
        headers = {"Authorization": f"Bearer {api_key}"}
        response = requests.get(auth_url, headers=headers, timeout=5)

        if response.status_code == 200:
            click.echo("✅ API Key验证通过")
            click.echo()
            click.echo("🎉 所有配置验证通过，客户端可以正常使用！")
        elif response.status_code == 401:
            click.echo("❌ API Key无效或已过期")
            click.echo("   请检查API Key是否正确或联系管理员")
        else:
            click.echo(f"⚠️  API Key验证异常: HTTP {response.status_code}")
    except Exception as e:
        click.echo(f"⚠️  API Key验证失败: {e}")
        click.echo("   服务端可能不支持验证端点，但基本连接正常")


@config.command()
@click.pass_context
def init(ctx):
    """交互式配置向导"""
    client_config = ClientConfig(
        cli_api_key=ctx.obj.get('cli_api_key'),
        cli_backend_url=ctx.obj.get('cli_backend_url')
    )

    click.echo("=== Zentao MCP Client 配置向导 ===")
    click.echo()

    # 检查是否已配置
    if client_config.is_configured():
        click.echo("客户端已配置:")
        info = client_config.get_config_info()
        click.echo(f"  后端URL: {info['backend_url']}")
        click.echo(f"  API Key: {info['api_key_preview']}")
        click.echo()

        reconfigure = click.confirm("是否重新配置?")
        if not reconfigure:
            return

    # 获取后端URL
    current_url = client_config.get_backend_url()
    default_url = current_url if current_url else "http://localhost:8000"

    backend_url = click.prompt(
        "请输入后端服务URL",
        default=default_url,
        type=str
    )

    # 确保URL格式正确
    if not backend_url.startswith(('http://', 'https://')):
        backend_url = f"http://{backend_url}"
    backend_url = backend_url.rstrip('/')

    # 获取API Key
    current_key = client_config.get_api_key()
    api_key = click.prompt(
        "请输入API Key",
        default=current_key if current_key else "",
        hide_input=True,
        type=str
    )

    # 保存配置
    client_config.set_backend_url(backend_url)
    client_config.set_api_key(api_key)

    click.echo()
    click.echo("✅ 配置已保存")
    click.echo(f"   配置文件: {client_config.config_file}")
    click.echo()
    click.echo("💡 使用 'zentao-mcp config check' 验证配置")


@config.command()
@click.argument('key', type=click.Choice(['api_key', 'backend_url']))
@click.argument('value')
@click.pass_context
def set(ctx, key, value):
    """设置配置项到用户目录"""
    client_config = ClientConfig(
        cli_api_key=ctx.obj.get('cli_api_key'),
        cli_backend_url=ctx.obj.get('cli_backend_url')
    )

    if key == 'api_key':
        client_config.set_api_key(value)
        click.echo("✅ API Key已设置")
    elif key == 'backend_url':
        # 确保URL格式正确
        if not value.startswith(('http://', 'https://')):
            value = f"http://{value}"
        value = value.rstrip('/')
        client_config.set_backend_url(value)
        click.echo("✅ Backend URL已设置")

    click.echo(f"   配置文件: {client_config.config_file}")


@config.command()
@click.pass_context
def reset(ctx):
    """重置用户配置"""
    client_config = ClientConfig(
        cli_api_key=ctx.obj.get('cli_api_key'),
        cli_backend_url=ctx.obj.get('cli_backend_url')
    )

    if not client_config.config_file.exists():
        click.echo("配置文件不存在，无需重置")
        return

    if click.confirm("确定要重置所有配置吗？此操作不可恢复"):
        client_config.config_file.unlink()
        click.echo("✅ 配置已重置")
        click.echo("💡 使用 'zentao-mcp config init' 重新配置")


@config.command()
def env_show():
    """显示当前环境变量状态"""
    import os

    click.echo("=== 环境变量状态 ===")
    click.echo()

    # 检查zentao相关环境变量
    env_vars = {
        'ZENTAO_MCP_API_KEY': os.getenv('ZENTAO_MCP_API_KEY'),
        'ZENTAO_MCP_BACKEND_URL': os.getenv('ZENTAO_MCP_BACKEND_URL'),
        'ZENTAO_MCP_BACKEND': os.getenv('ZENTAO_MCP_BACKEND')  # 兼容旧版本
    }

    has_env_vars = False
    for var_name, var_value in env_vars.items():
        if var_value:
            has_env_vars = True
            if var_name == 'ZENTAO_MCP_API_KEY':
                # API Key脱敏显示
                preview = f"{var_value[:2]}***{var_value[-2:]}" if len(var_value) > 4 else "***"
                click.echo(f"   {var_name} = {preview} ✓")
            else:
                click.echo(f"   {var_name} = {var_value} ✓")
        else:
            click.echo(f"   {var_name} = (未设置)")

    click.echo()
    if has_env_vars:
        click.echo("✅ 检测到zentao相关环境变量")
        click.echo("💡 环境变量优先级最高，会覆盖配置文件设置")
    else:
        click.echo("ℹ️  未检测到zentao相关环境变量")
        click.echo("💡 将使用配置文件或命令行参数设置")


@config.command()
def env_clear():
    """清理当前shell的zentao相关环境变量"""
    import os

    click.echo("=== 清理环境变量 ===")
    click.echo()

    # 检查当前环境变量
    env_vars = ['ZENTAO_MCP_API_KEY', 'ZENTAO_MCP_BACKEND_URL', 'ZENTAO_MCP_BACKEND']
    active_vars = [var for var in env_vars if os.getenv(var)]

    if not active_vars:
        click.echo("ℹ️  未检测到需要清理的环境变量")
        return

    click.echo("检测到以下环境变量:")
    for var in active_vars:
        value = os.getenv(var)
        if var == 'ZENTAO_MCP_API_KEY':
            preview = f"{value[:2]}***{value[-2:]}" if len(value) > 4 else "***"
            click.echo(f"   {var} = {preview}")
        else:
            click.echo(f"   {var} = {value}")

    click.echo()
    click.echo("请执行以下命令清理环境变量:")
    click.echo()
    click.echo(f"   unset {' '.join(active_vars)}")
    click.echo()
    click.echo("或者在新的shell会话中运行zentao-mcp命令")

@config.command()
@click.argument('env', type=click.Choice(['dev', 'test', 'prod']))
def env_export(env):
    """导出指定环境的配置为环境变量"""
    from pathlib import Path

    click.echo(f"=== 导出 {env} 环境配置 ===")
    click.echo()

    # 环境配置文件路径
    # 支持开发模式和打包模式
    script_dir = Path(__file__).parent.parent
    env_file = script_dir / "config" / "environments" / f"{env}.env"

    # 如果是打包的可执行文件，尝试从当前工作目录查找
    if not env_file.exists():
        cwd_env_file = Path.cwd() / "config" / "environments" / f"{env}.env"
        if cwd_env_file.exists():
            env_file = cwd_env_file

    if not env_file.exists():
        click.echo(f"❌ 环境配置文件不存在: {env_file}")
        click.echo("💡 请检查环境名称是否正确")
        return

    try:
        # 读取环境配置文件
        zentao_vars = {}
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                # 跳过注释和空行
                if not line or line.startswith('#'):
                    continue

                # 解析环境变量
                if '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip().strip('"\'')  # 移除引号

                    # 只导出zentao相关的环境变量
                    if key.startswith('ZENTAO_MCP_'):
                        zentao_vars[key] = value

        if not zentao_vars:
            click.echo(f"⚠️  在 {env} 环境配置中未找到zentao相关环境变量")
            return

        click.echo("请执行以下命令设置环境变量:")
        click.echo()
        for key, value in zentao_vars.items():
            click.echo(f"   export {key}='{value}'")

        click.echo()
        click.echo("或者使用以下命令一键设置:")
        click.echo(f"   eval $(zentao-mcp config env-export {env})")

    except Exception as e:
        click.echo(f"❌ 读取环境配置文件失败: {e}")




@main.command()
@click.option('--host', default='localhost', help='监听主机地址 (仅HTTP模式)')
@click.option('--port', default=8080, help='监听端口 (仅HTTP模式)')
@click.option('--mode', default='stdio', type=click.Choice(['stdio', 'http', 'sse']), help='运行模式')
def start(host: str, port: int, mode: str):
    """启动Zentao MCP客户端代理服务"""
    config = ClientConfig()

    if not config.is_configured():
        click.echo("❌ 客户端未配置，请先运行 'zentao-mcp config init'")
        return

    click.echo("🚀 启动Zentao MCP客户端代理服务...")
    click.echo(f"运行模式: {mode.upper()}")
    if mode == "http":
        click.echo(f"监听地址: http://{host}:{port}")
    click.echo(f"后端服务: {config.get_backend_url()}")
    click.echo()

    # 导入并启动代理服务
    start_proxy_server(host, port, config, mode)


if __name__ == '__main__':
    main()