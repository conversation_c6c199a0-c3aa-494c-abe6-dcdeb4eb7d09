#!/bin/bash
# ============================================================================
# Zentao MCP Client 统一部署脚本 v2.0
# 支持快速部署、交互式配置和传统命令行三种模式
# ============================================================================

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
SERVICE_DIR="$SCRIPT_DIR"

# 默认参数
ENVIRONMENT="dev"
ACTION="deploy"
DEPLOYMENT_TYPE=""
SERVICE_MODE="stdio"
HOST="localhost"
PORT=8080
FORCE_REBUILD=false
VERBOSE=false
CONTAINER_ENGINE=""
COMPOSE_CMD=""

# 加载公共函数库
if [[ -f "$PROJECT_ROOT/scripts/common.sh" ]]; then
    source "$PROJECT_ROOT/scripts/common.sh"
else
    echo "错误: 找不到公共函数库 $PROJECT_ROOT/scripts/common.sh"
    exit 1
fi

# 显示横幅
show_banner() {
    echo -e "\n${BLUE}============================================================${NC}"
    echo -e "${WHITE}    Zentao MCP Client 部署脚本 v2.0${NC}"
    echo -e "${BLUE}============================================================${NC}\n"
}

# 设置环境变量
setup_environment_variables() {
    local env=$1

    # 环境变量配置文件路径
    local env_file="$SERVICE_DIR/config/environments/${env}.env"

    if [[ -f "$env_file" ]]; then
        log_info "加载环境变量配置: $env_file"

        # 读取并导出环境变量
        while IFS='=' read -r key value; do
            # 跳过注释和空行
            [[ "$key" =~ ^[[:space:]]*# ]] && continue
            [[ -z "$key" ]] && continue

            # 移除值两端的引号
            value=$(echo "$value" | sed 's/^["'\'']//' | sed 's/["'\'']$//')

            # 导出环境变量
            if [[ -n "$key" && -n "$value" ]]; then
                export "$key"="$value"
                log_debug "设置环境变量: $key=$value"
            fi
        done < <(grep -v '^[[:space:]]*$' "$env_file" | grep -v '^[[:space:]]*#')

        log_success "环境变量设置完成"
    else
        log_warning "环境变量配置文件不存在: $env_file"
    fi
}

# 显示主菜单
show_main_menu() {
    echo -e "${WHITE}选择操作模式:${NC}\n"

    echo -e "${GREEN}1. 快速部署 (使用预设配置)${NC}"
    echo -e "   描述: 一键部署常用环境配置\n"

    echo -e "${BLUE}2. 交互式配置 (自定义所有参数)${NC}"
    echo -e "   描述: 逐步选择所有部署参数，带配置验证\n"

    echo -e "${YELLOW}3. 传统命令行模式 (显示帮助信息)${NC}"
    echo -e "   描述: 兼容原有命令行用法\n"

    while true; do
        read -p "请选择 [1-3] (默认: 1): " choice
        case ${choice:-1} in
            1) return 1 ;;
            2) return 2 ;;
            3) return 3 ;;
            *) log_error "请输入 1-3 之间的数字" ;;
        esac
    done
}

# 显示快速部署菜单
show_quick_deploy_menu() {
    echo -e "\n${WHITE}=== 快速部署选项 ===${NC}\n"

    echo -e "${GREEN}1. [推荐] 开发环境${NC}"
    echo -e "   配置: dev + source + pc + stdio\n"

    echo -e "${BLUE}2. 测试环境${NC}"
    echo -e "   配置: test + executable + container + http\n"

    echo -e "${PURPLE}3. 生产环境${NC}"
    echo -e "   配置: prod + executable + container + http\n"

    while true; do
        read -p "请选择 [1-3] (默认: 1): " choice
        case ${choice:-1} in
            1)
                ENVIRONMENT="dev"
                DEPLOYMENT_TYPE="source"
                RUNTIME_ENV="pc"
                SERVICE_MODE="stdio"
                return 0
                ;;
            2)
                ENVIRONMENT="test"
                DEPLOYMENT_TYPE="executable"
                RUNTIME_ENV="container"
                SERVICE_MODE="http"
                HOST="0.0.0.0"
                return 0
                ;;
            3)
                ENVIRONMENT="prod"
                DEPLOYMENT_TYPE="executable"
                RUNTIME_ENV="container"
                SERVICE_MODE="http"
                HOST="0.0.0.0"
                return 0
                ;;
            *) log_error "请输入 1-3 之间的数字" ;;
        esac
    done
}

# 交互式配置
interactive_config() {
    echo -e "\n${WHITE}=== 交互式配置向导 ===${NC}\n"

    # 步骤1: 选择环境
    echo -e "${CYAN}步骤 1/4: 选择环境${NC}"
    echo "  1. dev (开发环境)"
    echo "  2. test (测试环境)"
    echo "  3. prod (生产环境)"

    while true; do
        read -p "请选择 [1-3]: " choice
        case $choice in
            1) ENVIRONMENT="dev"; break ;;
            2) ENVIRONMENT="test"; break ;;
            3) ENVIRONMENT="prod"; break ;;
            *) log_error "请输入 1-3 之间的数字" ;;
        esac
    done
    echo -e "✓ 已选择环境: ${GREEN}$ENVIRONMENT${NC}\n"

    # 步骤2: 选择安装方式
    echo -e "${CYAN}步骤 2/4: 选择安装方式${NC}"
    echo "  1. source (源码运行)"
    echo "  2. pip (pip安装)"
    echo "  3. executable (可执行文件)"

    while true; do
        read -p "请选择 [1-3]: " choice
        case $choice in
            1) DEPLOYMENT_TYPE="source"; break ;;
            2) DEPLOYMENT_TYPE="pip"; break ;;
            3) DEPLOYMENT_TYPE="executable"; break ;;
            *) log_error "请输入 1-3 之间的数字" ;;
        esac
    done
    echo -e "✓ 已选择安装方式: ${GREEN}$DEPLOYMENT_TYPE${NC}\n"

    # 步骤3: 选择运行环境
    echo -e "${CYAN}步骤 3/4: 选择运行环境${NC}"
    echo "  1. pc (PC环境)"
    echo "  2. container (容器环境)"

    while true; do
        read -p "请选择 [1-2]: " choice
        case $choice in
            1) RUNTIME_ENV="pc"; break ;;
            2) RUNTIME_ENV="container"; break ;;
            *) log_error "请输入 1-2 之间的数字" ;;
        esac
    done
    echo -e "✓ 已选择运行环境: ${GREEN}$RUNTIME_ENV${NC}\n"

    # 步骤4: 选择运行方式 (带验证)
    while true; do
        echo -e "${CYAN}步骤 4/4: 选择运行方式${NC}"
        echo "  1. stdio (标准输入输出)"
        echo "  2. http (HTTP服务)"
        echo "  3. sse (服务器发送事件)"

        read -p "请选择 [1-3]: " choice
        case $choice in
            1) SERVICE_MODE="stdio" ;;
            2) SERVICE_MODE="http" ;;
            3) SERVICE_MODE="sse" ;;
            *) log_error "请输入 1-3 之间的数字"; continue ;;
        esac

        # 配置验证
        if validate_config "$ENVIRONMENT" "$DEPLOYMENT_TYPE" "$RUNTIME_ENV" "$SERVICE_MODE"; then
            echo -e "✓ 已选择运行方式: ${GREEN}$SERVICE_MODE${NC}\n"
            break
        else
            echo -e "${RED}[配置验证] ❌ 配置组合无效，请重新选择${NC}\n"
        fi
    done

    # 网络配置
    if [[ "$SERVICE_MODE" == "http" || "$SERVICE_MODE" == "sse" ]]; then
        echo -e "${CYAN}网络配置:${NC}"
        read -p "监听地址 (默认: localhost): " host_input
        HOST=${host_input:-localhost}

        read -p "端口号 (默认: 8080): " port_input
        if [[ -n "$port_input" && "$port_input" =~ ^[0-9]+$ ]]; then
            PORT=$port_input
        else
            PORT=8080
        fi
        echo
    fi
}

# 配置验证
validate_config() {
    local env=$1 type=$2 runtime=$3 mode=$4

    # 规则1: 容器环境不支持stdio模式
    if [[ "$runtime" == "container" && "$mode" == "stdio" ]]; then
        log_error "容器环境不支持stdio模式，请选择http或sse"
        return 1
    fi

    # 规则2: 容器环境只支持executable安装
    if [[ "$runtime" == "container" && "$type" != "executable" ]]; then
        log_error "容器环境只支持executable安装方式"
        return 1
    fi

    return 0
}

# 显示配置摘要
show_config_summary() {
    echo -e "${WHITE}=== 部署配置摘要 ===${NC}"
    echo -e "环境: ${GREEN}$ENVIRONMENT${NC}"
    echo -e "安装方式: ${GREEN}$DEPLOYMENT_TYPE${NC}"
    echo -e "运行环境: ${GREEN}$RUNTIME_ENV${NC}"
    echo -e "运行方式: ${GREEN}$SERVICE_MODE${NC}"

    if [[ "$SERVICE_MODE" != "stdio" ]]; then
        echo -e "监听地址: ${GREEN}$HOST:$PORT${NC}"
    fi
    echo
}

# 确认部署
confirm_deployment() {
    while true; do
        read -p "确认开始部署？ [y/N]: " confirm
        case $confirm in
            [Yy]|[Yy][Ee][Ss]) return 0 ;;
            [Nn]|[Nn][Oo]|"") return 1 ;;
            *) log_error "请输入 y 或 n" ;;
        esac
    done
}

# 显示传统帮助信息
show_traditional_help() {
    cat << 'EOF'
Zentao MCP Client 部署脚本 v2.0

用法: ./deploy.sh <环境> <动作> [选项]

环境:
  dev       开发环境 (默认) - 源码运行，项目配置
  test      测试环境 - 容器部署，环境变量配置
  prod      生产环境 - 容器部署，系统服务

动作:
  deploy    部署客户端服务 (默认)
  build     构建制品
  start     启动服务
  stop      停止服务
  restart   重启服务
  status    查看服务状态
  config    配置向导
  test      运行连接测试
  clean     清理环境

选项:
  -t, --type TYPE       部署类型 (source|pip|executable|container)
  -m, --mode MODE       服务模式 (stdio|http|sse)
  --host HOST           HTTP模式监听地址 (默认: localhost)
  --port PORT           HTTP/SSE模式端口 (默认: 8080)
  -f, --force           强制重新构建
  -v, --verbose         详细输出模式
  -h, --help            显示帮助信息

快速模式:
  quick [1-3]           快速部署 (1=dev, 2=test, 3=prod)
  -q [1-3]              快速部署简写
  interactive           交互式配置
  -i                    交互式配置简写

示例:
  # 菜单模式
  ./deploy.sh                           # 启动主菜单
  ./deploy.sh quick 1                   # 快速部署开发环境
  ./deploy.sh -i                        # 交互式配置

  # 部署命令
  ./deploy.sh dev deploy                # dev环境源码部署
  ./deploy.sh dev deploy --type=pip     # dev环境pip部署
  ./deploy.sh test deploy --mode=http   # test环境容器部署
  ./deploy.sh prod deploy --mode=sse    # prod环境容器部署

  # 构建命令
  ./deploy.sh dev build --type=executable  # 构建可执行文件
  ./deploy.sh test build --force           # 强制重建容器镜像
  ./deploy.sh prod build                   # 构建生产容器镜像

  # 服务管理
  ./deploy.sh dev start                    # 启动dev环境服务
  ./deploy.sh test start --mode=http       # 启动test容器服务
  ./deploy.sh prod stop --mode=http        # 停止prod容器服务
  ./deploy.sh test restart --mode=http     # 重启test容器服务

  # 状态查看
  ./deploy.sh dev status                   # 查看dev环境状态
  ./deploy.sh test status --mode=http      # 查看test容器状态
  ./deploy.sh prod status --mode=sse       # 查看prod容器状态

  # 配置管理
  ./deploy.sh dev config                   # dev环境配置向导
  ./deploy.sh test config --mode=http      # test容器配置向导
  ./deploy.sh prod config --mode=sse       # prod容器配置向导

  # 测试和清理
  ./deploy.sh dev test                     # dev环境连接测试
  ./deploy.sh test test --mode=http        # test环境连接测试
  ./deploy.sh dev clean --type=pip         # 清理dev环境pip安装
  ./deploy.sh test clean --mode=http       # 清理test容器环境

EOF
}

# 执行进度显示
execute_with_progress() {
    local description=$1
    local command=$2
    local show_output=${3:-"on_error"}

    log_info "$description"

    if [[ "$VERBOSE" == "true" || "$show_output" == "always" ]]; then
        eval "$command"
    else
        if eval "$command" >/dev/null 2>&1; then
            return 0
        else
            local exit_code=$?
            log_error "$description 失败"
            if [[ "$show_output" == "on_error" ]]; then
                log_error "重新执行以显示详细错误信息..."
                eval "$command"
            fi
            return $exit_code
        fi
    fi
}

# 源码部署
deploy_from_source() {
    local env=$1
    cd "$SERVICE_DIR"
    execute_with_progress "安装Python依赖 (uv sync)" "uv sync" "always"
    log_success "源码部署完成"
}

# pip安装部署
deploy_with_pip() {
    local env=$1
    cd "$SERVICE_DIR"
    execute_with_progress "安装zentao-mcp包 (pip)" "pip install -e ." "always"
    log_success "pip部署完成"
}

# 构建可执行文件
build_executable() {
    local env=$1
    cd "$SERVICE_DIR"

    # 检查PyInstaller
    if ! python3 -c "import PyInstaller" 2>/dev/null; then
        execute_with_progress "安装PyInstaller" "pip install pyinstaller" "always"
    fi

    # 运行构建脚本
    execute_with_progress "构建 $env 环境可执行文件" "python3 build_executable.py" "always"

    # 检查构建结果
    if [[ -f "dist/zentao-mcp" ]]; then
        local size_mb=$(du -m "dist/zentao-mcp" | cut -f1)
        log_success "可执行文件构建完成 (${size_mb}MB)"
    else
        log_error "可执行文件构建失败"
        return 1
    fi
}

# 可执行文件部署
deploy_executable() {
    local env=$1
    log_info "使用可执行文件部署 $env 环境..."
    cd "$SERVICE_DIR"

    # 如果可执行文件不存在，先构建
    if [[ ! -f "dist/zentao-mcp" ]] || [[ "$FORCE_REBUILD" == "true" ]]; then
        build_executable "$env"
    fi

    # 复制到系统目录
    local install_dir="/usr/local/bin"
    if [[ -w "$install_dir" ]]; then
        cp "dist/zentao-mcp" "$install_dir/"
        chmod +x "$install_dir/zentao-mcp"
        log_success "可执行文件已安装到: $install_dir/zentao-mcp"
    else
        log_warning "无权限安装到系统目录，请手动复制:"
        log_warning "sudo cp dist/zentao-mcp /usr/local/bin/"
    fi
}

# 执行部署
execute_deployment() {
    # 确定部署类型
    if [[ "$RUNTIME_ENV" == "container" ]]; then
        DEPLOYMENT_TYPE="container"
    fi

    # 执行对应的动作
    case $ACTION in
        deploy)
            log_info "执行部署步骤: BUILD → START"
            case $DEPLOYMENT_TYPE in
                source)
                    deploy_from_source "$ENVIRONMENT"
                    ;;
                pip)
                    deploy_with_pip "$ENVIRONMENT"
                    ;;
                executable)
                    deploy_executable "$ENVIRONMENT"
                    ;;
                container)
                    deploy_with_container "$ENVIRONMENT"
                    ;;
            esac
            log_success "部署完成！"
            ;;
        build)
            if [[ "$DEPLOYMENT_TYPE" == "executable" ]]; then
                build_executable "$ENVIRONMENT"
            elif [[ "$DEPLOYMENT_TYPE" == "container" ]]; then
                manage_container_service "$ENVIRONMENT" "build"
            else
                log_warning "构建动作仅适用于executable或container部署类型"
            fi
            ;;
        start)
            if [[ "$DEPLOYMENT_TYPE" == "container" ]]; then
                manage_container_service "$ENVIRONMENT" "start"
            else
                start_service "$ENVIRONMENT"
            fi
            ;;
        stop)
            if [[ "$DEPLOYMENT_TYPE" == "container" ]]; then
                manage_container_service "$ENVIRONMENT" "stop"
            else
                stop_service "$ENVIRONMENT"
            fi
            ;;
        restart)
            if [[ "$DEPLOYMENT_TYPE" == "container" ]]; then
                manage_container_service "$ENVIRONMENT" "restart"
            else
                stop_service "$ENVIRONMENT"
                sleep 2
                start_service "$ENVIRONMENT"
            fi
            ;;
        status)
            if [[ "$DEPLOYMENT_TYPE" == "container" ]]; then
                manage_container_service "$ENVIRONMENT" "status"
            else
                show_service_status "$ENVIRONMENT"
            fi
            ;;
        config)
            run_config_wizard "$ENVIRONMENT"
            ;;
        test)
            run_connection_test "$ENVIRONMENT"
            ;;
        clean)
            if [[ "$DEPLOYMENT_TYPE" == "container" ]]; then
                manage_container_service "$ENVIRONMENT" "clean"
            else
                clean_environment "$ENVIRONMENT"
            fi
            ;;
        *)
            log_error "动作 $ACTION 暂未实现"
            show_traditional_help
            exit 1
            ;;
    esac
}

# 容器化部署
deploy_with_container() {
    local env=$1
    cd "$SERVICE_DIR"

    # 检查配置文件
    local compose_file="config/docker-compose.yml"
    local env_file="config/environments/${env}.env"
    if [[ ! -f "$compose_file" ]]; then
        log_error "Compose配置文件不存在: $compose_file"
        return 1
    fi
    if [[ ! -f "$env_file" ]]; then
        log_error "环境配置文件不存在: $env_file"
        return 1
    fi

    # 检测容器引擎并设置compose命令
    detect_container_engine || exit 1
    setup_compose_command || exit 1

    # 设置环境变量
    export ENVIRONMENT="$env"

    # 构建和启动服务
    local build_args=""
    if [[ "$FORCE_REBUILD" == "true" ]]; then
        build_args="$build_args --no-cache"
    fi

    local build_cmd="$COMPOSE_CMD -f '$compose_file' --env-file '$env_file' build $build_args"
    execute_with_progress "构建 $env 环境客户端容器镜像" "$build_cmd" "always"

    local deploy_cmd="$COMPOSE_CMD -f '$compose_file' --env-file '$env_file' up -d"
    execute_with_progress "启动 $env 环境客户端容器服务" "$deploy_cmd" "always"
}

# 启动服务
start_service() {
    local env=$1

    log_info "启动 $env 环境服务 (模式: $SERVICE_MODE)..."

    # 设置环境变量
    setup_environment_variables "$env"

    local start_cmd=""
    local start_args="start --mode $SERVICE_MODE"

    if [[ "$SERVICE_MODE" == "http" ]]; then
        start_args="$start_args --host $HOST --port $PORT"
    elif [[ "$SERVICE_MODE" == "sse" ]]; then
        start_args="$start_args --port $PORT"
    fi

    case $DEPLOYMENT_TYPE in
        source)
            cd "$SERVICE_DIR"
            start_cmd="uv run python -m zentao_mcp $start_args"
            ;;
        pip)
            start_cmd="zentao-mcp $start_args"
            ;;
        executable)
            if [[ -f "/usr/local/bin/zentao-mcp" ]]; then
                start_cmd="/usr/local/bin/zentao-mcp $start_args"
            elif [[ -f "$SERVICE_DIR/dist/zentao-mcp" ]]; then
                start_cmd="$SERVICE_DIR/dist/zentao-mcp $start_args"
            else
                log_error "找不到zentao-mcp可执行文件"
                return 1
            fi
            ;;
    esac

    log_info "执行启动命令: $start_cmd"

    if [[ "$SERVICE_MODE" == "stdio" ]]; then
        log_info "STDIO模式启动，按Ctrl+C停止服务"
        eval "$start_cmd"
    else
        log_info "后台启动服务..."
        nohup $start_cmd > "/tmp/zentao-mcp-client-${env}.log" 2>&1 &
        local pid=$!
        echo $pid > "/tmp/zentao-mcp-client-${env}.pid"
        log_success "服务已启动 (PID: $pid)"

        if [[ "$SERVICE_MODE" == "http" ]]; then
            log_info "HTTP服务地址: http://$HOST:$PORT"
        fi
    fi
}

# 停止服务
stop_service() {
    local env=$1

    log_info "停止 $env 环境服务..."

    local pid_file="/tmp/zentao-mcp-client-${env}.pid"

    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            kill "$pid"
            rm -f "$pid_file"
            log_success "服务已停止 (PID: $pid)"
        else
            log_warning "服务进程不存在 (PID: $pid)"
            rm -f "$pid_file"
        fi
    else
        log_warning "未找到PID文件，尝试查找进程..."
        pkill -f "zentao-mcp" || log_warning "未找到运行中的服务"
    fi
}

# 查看服务状态
show_service_status() {
    local env=$1

    log_info "查看 $env 环境服务状态..."

    local pid_file="/tmp/zentao-mcp-client-${env}.pid"

    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            log_success "服务运行中 (PID: $pid)"

            # 显示进程信息
            ps -p "$pid" -o pid,ppid,command 2>/dev/null || ps -p "$pid" 2>/dev/null || true

            # 如果是HTTP模式，测试连接
            if [[ "$SERVICE_MODE" == "http" ]]; then
                if curl -f -s "http://$HOST:$PORT/health" &> /dev/null; then
                    log_success "HTTP健康检查通过"
                else
                    log_warning "HTTP健康检查失败"
                fi
            fi
        else
            log_warning "服务进程不存在 (PID: $pid)"
            rm -f "$pid_file"
        fi
    else
        log_warning "服务未运行"
    fi
}

# 运行连接测试
run_connection_test() {
    local env=$1

    log_info "运行 $env 环境连接测试..."

    cd "$SERVICE_DIR"

    case $DEPLOYMENT_TYPE in
        source)
            if [[ -f "test_client_connect.py" ]]; then
                uv run python test_client_connect.py
            else
                log_warning "测试文件不存在: test_client_connect.py"
            fi
            ;;
        pip|executable)
            # 测试后端服务连接
            log_info "测试后端服务连接..."
            local backend_url="${ZENTAO_MCP_BACKEND_URL:-http://localhost:8000}"
            if curl -f -s "$backend_url/health" &> /dev/null; then
                log_success "后端服务连接正常"
            else
                log_error "后端服务连接失败: $backend_url"
                return 1
            fi
            ;;
    esac

    log_success "连接测试完成"
}

# 清理环境
clean_environment() {
    local env=$1

    log_warning "清理 $env 环境..."

    # 停止服务
    stop_service "$env"

    # 清理临时文件
    rm -f "/tmp/zentao-mcp-client-${env}.log"
    rm -f "/tmp/zentao-mcp-client-${env}.pid"

    case $DEPLOYMENT_TYPE in
        source)
            cd "$SERVICE_DIR"
            # 清理Python缓存
            find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
            find . -name "*.pyc" -delete 2>/dev/null || true
            # 清理构建文件
            rm -rf build/ dist/ *.egg-info/ .pytest_cache/
            ;;
        pip)
            log_info "卸载zentao-mcp包..."
            pip uninstall -y zentao-mcp || true
            ;;
        executable)
            # 删除可执行文件
            rm -f "/usr/local/bin/zentao-mcp"
            cd "$SERVICE_DIR"
            rm -rf dist/ build/
            ;;
    esac

    log_success "环境清理完成"
}

# 配置向导
run_config_wizard() {
    local env=$1

    log_info "启动 $env 环境配置向导..."

    case $DEPLOYMENT_TYPE in
        source)
            cd "$SERVICE_DIR"
            uv run python -m zentao_mcp configure
            ;;
        pip)
            zentao-mcp configure
            ;;
        executable)
            if [[ -f "/usr/local/bin/zentao-mcp" ]]; then
                /usr/local/bin/zentao-mcp configure
            elif [[ -f "$SERVICE_DIR/dist/zentao-mcp" ]]; then
                "$SERVICE_DIR/dist/zentao-mcp" configure
            else
                log_error "找不到zentao-mcp可执行文件"
                return 1
            fi
            ;;
        container)
            log_info "容器环境配置向导..."
            log_info "请编辑环境配置文件: config/environments/${env}.env"
            log_info "主要配置项："
            log_info "  - ZENTAO_MCP_BACKEND_URL: 后端服务地址"
            log_info "  - ZENTAO_MCP_API_KEY: API密钥"
            log_info "  - SERVICE_MODE: 服务模式 (http|sse)"
            log_info "  - HOST: 监听地址"
            log_info "  - PORT: 监听端口"

            # 检查配置文件是否存在
            local env_file="config/environments/${env}.env"
            if [[ -f "$env_file" ]]; then
                log_info "当前配置文件内容："
                cat "$env_file"
            else
                log_warning "配置文件不存在: $env_file"
            fi
            ;;
    esac
}

# 容器化服务管理
manage_container_service() {
    local env=$1
    local action=$2

    cd "$SERVICE_DIR"

    local compose_file="config/docker-compose.yml"
    local env_file="config/environments/${env}.env"
    if [[ ! -f "$compose_file" ]]; then
        log_error "Compose配置文件不存在: $compose_file"
        return 1
    fi
    if [[ ! -f "$env_file" ]]; then
        log_error "环境配置文件不存在: $env_file"
        return 1
    fi

    # 设置环境变量
    export ENVIRONMENT="$env"

    # 检测容器引擎并设置compose命令
    detect_container_engine || exit 1
    setup_compose_command || exit 1

    case $action in
        build)
            log_info "构建 $env 环境容器镜像..."
            local build_args=""
            if [[ "$FORCE_REBUILD" == "true" ]]; then
                build_args="$build_args --no-cache"
            fi
            $COMPOSE_CMD -f "$compose_file" --env-file "$env_file" build $build_args
            ;;
        start)
            log_info "启动 $env 环境容器服务..."
            $COMPOSE_CMD -f "$compose_file" --env-file "$env_file" start
            ;;
        stop)
            log_info "停止 $env 环境容器服务..."
            $COMPOSE_CMD -f "$compose_file" --env-file "$env_file" stop
            ;;
        restart)
            log_info "重启 $env 环境容器服务..."
            $COMPOSE_CMD -f "$compose_file" --env-file "$env_file" restart
            ;;
        status)
            log_info "查看 $env 环境容器状态..."
            $COMPOSE_CMD -f "$compose_file" --env-file "$env_file" ps
            ;;
        logs)
            log_info "查看 $env 环境容器日志..."
            $COMPOSE_CMD -f "$compose_file" --env-file "$env_file" logs -f
            ;;
        clean)
            log_warning "清理 $env 环境容器..."
            if [[ "$FORCE_REBUILD" != "true" ]]; then
                echo -n "确认清理 $env 环境容器？这将删除所有容器和卷 [y/N]: "
                read -r confirm
                if [[ "$confirm" != "y" && "$confirm" != "Y" ]]; then
                    log_info "取消清理操作"
                    return 0
                fi
            fi
            $COMPOSE_CMD -f "$compose_file" --env-file "$env_file" down --volumes --remove-orphans || true
            ;;
    esac
}

# 主函数
main() {
    # 检查是否有参数
    if [[ $# -eq 0 ]]; then
        # 无参数，启动主菜单
        show_banner
        show_main_menu
        local menu_choice=$?

        case $menu_choice in
            1)
                show_quick_deploy_menu
                ;;
            2)
                interactive_config
                ;;
            3)
                show_traditional_help
                exit 0
                ;;
        esac

        # 显示配置摘要并确认
        show_config_summary
        if confirm_deployment; then
            execute_deployment
        else
            log_info "部署已取消"
            exit 0
        fi
        return
    fi

    # 检查快速模式
    case $1 in
        quick|-q)
            show_banner
            if [[ -n "$2" && "$2" =~ ^[1-3]$ ]]; then
                case $2 in
                    1) ENVIRONMENT="dev"; DEPLOYMENT_TYPE="source"; RUNTIME_ENV="pc"; SERVICE_MODE="stdio" ;;
                    2) ENVIRONMENT="test"; DEPLOYMENT_TYPE="executable"; RUNTIME_ENV="container"; SERVICE_MODE="http"; HOST="0.0.0.0" ;;
                    3) ENVIRONMENT="prod"; DEPLOYMENT_TYPE="executable"; RUNTIME_ENV="container"; SERVICE_MODE="http"; HOST="0.0.0.0" ;;
                esac
                show_config_summary
                if confirm_deployment; then
                    execute_deployment
                else
                    log_info "部署已取消"
                fi
            else
                show_quick_deploy_menu
                show_config_summary
                if confirm_deployment; then
                    execute_deployment
                else
                    log_info "部署已取消"
                fi
            fi
            return
            ;;
        interactive|-i)
            show_banner
            interactive_config
            show_config_summary
            if confirm_deployment; then
                execute_deployment
            else
                log_info "部署已取消"
            fi
            return
            ;;
    esac

    # 传统命令行模式 - 解析参数
    parse_traditional_args "$@"
}

# 解析传统命令行参数
parse_traditional_args() {
    # 优先检查help参数
    for arg in "$@"; do
        if [[ "$arg" == "-h" || "$arg" == "--help" ]]; then
            show_traditional_help
            exit 0
        fi
    done

    # 解析命令行参数
    local remaining_args=()
    while [[ $# -gt 0 ]]; do
        case $1 in
            -t|--type)
                DEPLOYMENT_TYPE="$2"
                shift 2
                ;;
            --type=*)
                DEPLOYMENT_TYPE="${1#*=}"
                shift
                ;;
            -m|--mode)
                SERVICE_MODE="$2"
                shift 2
                ;;
            --mode=*)
                SERVICE_MODE="${1#*=}"
                shift
                ;;
            --host)
                HOST="$2"
                shift 2
                ;;
            --host=*)
                HOST="${1#*=}"
                shift
                ;;
            --port)
                PORT="$2"
                shift 2
                ;;
            --port=*)
                PORT="${1#*=}"
                shift
                ;;
            -f|--force)
                FORCE_REBUILD=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            dev|test|prod)
                if [[ -z "$ENVIRONMENT" || "$ENVIRONMENT" == "dev" ]]; then
                    ENVIRONMENT="$1"
                else
                    # 如果环境已设置，这可能是动作
                    if [[ "$1" == "test" ]]; then
                        ACTION="test"
                    else
                        remaining_args+=("$1")
                    fi
                fi
                shift
                ;;
            deploy|build|start|stop|restart|status|config|clean)
                ACTION="$1"
                shift
                ;;
            *)
                remaining_args+=("$1")
                shift
                ;;
        esac
    done

    # 检查是否有未识别的参数
    if [[ ${#remaining_args[@]} -gt 0 ]]; then
        log_error "未识别的参数: ${remaining_args[*]}"
        log_error "请使用 --help 查看正确的使用方法"
        show_traditional_help
        exit 1
    fi

    # 验证环境参数
    if [[ ! "$ENVIRONMENT" =~ ^(dev|test|prod)$ ]]; then
        log_error "无效的环境: $ENVIRONMENT"
        log_error "支持的环境: dev, test, prod"
        exit 1
    fi

    # 验证服务模式
    if [[ ! "$SERVICE_MODE" =~ ^(stdio|http|sse)$ ]]; then
        log_error "无效的服务模式: $SERVICE_MODE"
        log_error "支持的模式: stdio, http, sse"
        exit 1
    fi

    # 确定部署类型
    if [[ -z "$DEPLOYMENT_TYPE" ]]; then
        case $ENVIRONMENT in
            dev)
                DEPLOYMENT_TYPE="source"
                ;;
            test)
                DEPLOYMENT_TYPE="container"
                ;;
            prod)
                DEPLOYMENT_TYPE="container"
                ;;
        esac
    fi

    # 验证部署类型
    if [[ ! "$DEPLOYMENT_TYPE" =~ ^(source|pip|executable|container)$ ]]; then
        log_error "无效的部署类型: $DEPLOYMENT_TYPE"
        log_error "支持的类型: source, pip, executable, container"
        exit 1
    fi

    # 验证环境和部署类型的兼容性
    case $ENVIRONMENT in
        dev)
            # dev环境支持所有部署类型，但容器部署不支持stdio模式
            if [[ "$DEPLOYMENT_TYPE" == "container" && "$SERVICE_MODE" == "stdio" ]]; then
                log_error "dev环境的容器部署不支持stdio模式，请使用http或sse"
                exit 1
            fi
            ;;
        test|prod)
            # test/prod环境推荐使用容器部署
            if [[ "$DEPLOYMENT_TYPE" != "container" ]]; then
                log_warning "$ENVIRONMENT 环境推荐使用container部署类型"
            fi
            # 容器部署不支持stdio模式
            if [[ "$DEPLOYMENT_TYPE" == "container" && "$SERVICE_MODE" == "stdio" ]]; then
                log_error "$ENVIRONMENT 环境的容器部署不支持stdio模式，请使用http或sse"
                exit 1
            fi
            ;;
    esac

    # 显示配置信息
    log_info "使用配置:"
    log_info "  环境: $ENVIRONMENT"
    log_info "  动作: $ACTION"
    log_info "  部署类型: $DEPLOYMENT_TYPE"
    log_info "  服务模式: $SERVICE_MODE"
    if [[ "$SERVICE_MODE" != "stdio" ]]; then
        log_info "  监听地址: $HOST:$PORT"
    fi

    # 执行部署
    execute_deployment
}

# 执行主函数
main "$@"
