#!/usr/bin/env python3
"""
Zentao MCP Client 主入口文件
用于PyInstaller构建可执行文件
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 修复PyInstaller中importlib.metadata的问题
try:
    import importlib.metadata
    # 预加载fastmcp的元数据
    try:
        importlib.metadata.version("fastmcp")
    except importlib.metadata.PackageNotFoundError:
        # 如果找不到元数据，设置一个默认版本
        import fastmcp
        if not hasattr(fastmcp, '__version__'):
            fastmcp.__version__ = "2.11.3"  # 设置默认版本
except ImportError:
    pass

from zentao_mcp.cli import main

if __name__ == '__main__':
    main()
