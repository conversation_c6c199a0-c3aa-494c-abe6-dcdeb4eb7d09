# Zentao MCP Client - 轻量级禅道MCP客户端

[![Python Version](https://img.shields.io/badge/python-3.10+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![PyPI Version](https://img.shields.io/pypi/v/zentao-mcp-client.svg)](https://pypi.org/project/zentao-mcp-client/)

Zentao MCP Client 是一个轻量级的禅道MCP（Model Context Protocol）客户端代理，提供了简单易用的命令行界面和多种运行模式，让您可以轻松地与禅道MCP后端服务进行交互。

## ✨ 特性

- 🚀 **多种运行模式**: 支持STDIO、HTTP、SSE三种模式
- 🔧 **灵活配置**: 支持环境变量、命令行参数、配置文件多层级配置
- 🛡️ **安全认证**: 支持API Key认证，保障数据安全
- 📦 **轻量级**: 最小化依赖，快速启动
- 🔄 **完整工具支持**: 支持所有27个禅道MCP工具
- 📊 **实时监控**: 内置健康检查和性能监控
- 🐳 **多种部署方式**: 支持源码运行、pip安装、独立可执行文件、容器部署
- ⚙️ **统一部署脚本**: 提供统一的部署脚本，支持dev/test/prod环境

## 📋 系统要求

- Python 3.10 或更高版本
- 网络连接到禅道MCP后端服务

## 🚀 快速开始

### 方式一：通过pip安装（推荐）

```bash
# 从PyPI安装
pip install zentao-mcp-client

# 或从测试PyPI安装最新版本
pip install --index-url https://test.pypi.org/simple/ zentao-mcp-client
```

### 方式二：使用统一部署脚本（推荐开发环境）

```bash
# 克隆仓库
git clone <repository-url>
cd zentao-mcp-client

# 使用部署脚本快速启动
./deploy.sh dev deploy
```

### 方式三：从源码安装

```bash
# 克隆仓库
git clone <repository-url>
cd zentao-mcp-client

# 安装依赖并安装
pip install -e .
```

## 🔧 配置

Zentao MCP Client 支持多层级配置，按优先级从高到低：

### 配置优先级

1. **环境变量** (最高优先级)
2. **命令行参数** (第二优先级)
3. **用户目录配置文件** (最低优先级)

### 配置方式

#### 方式一：环境变量（推荐生产环境）

```bash
export ZENTAO_MCP_BACKEND_URL="http://your-server:8000"
export ZENTAO_MCP_API_KEY="your-api-key"
```

#### 方式二：命令行参数

```bash
zentao-mcp --api-key="your-api-key" --backend-url="http://your-server:8000" start
```

#### 方式三：交互式配置

```bash
zentao-mcp configure
```

按提示输入后端服务URL和API Key，配置将保存到用户目录。

### 配置管理命令

```bash
# 显示所有配置来源和当前使用值
zentao-mcp config show

# 验证配置有效性和服务端连接
zentao-mcp config check

# 设置配置项到用户目录
zentao-mcp config set api_key "your-api-key"
zentao-mcp config set backend_url "http://your-server:8000"

# 重置用户目录配置
zentao-mcp config reset
```

## 💻 使用

### 基本命令

```bash
# 启动客户端代理服务（STDIO模式）
zentao-mcp start

# 启动HTTP服务模式
zentao-mcp start --mode http --host 0.0.0.0 --port 8080

# 启动SSE服务模式
zentao-mcp start --mode sse --port 8080

# 查看配置信息
zentao-mcp info

# 查看帮助
zentao-mcp --help
```

### 使用统一部署脚本

```bash
# 开发环境快速启动
./deploy.sh dev start

# 测试环境容器部署
./deploy.sh test deploy --mode http

# 生产环境容器部署
./deploy.sh prod deploy --mode http

# 查看部署脚本帮助
./deploy.sh --help
```

## 🎯 功能特性

- 🔄 **完全兼容**: 支持HTTP/STDIO/SSE三种运行模式
- 🌐 **代理转发**: 自动转发请求到后端服务
- 🛠️ **完整工具集**: 支持所有27个Zentao MCP工具和资源
- ⚙️ **灵活配置**: 多层级配置系统，适应不同部署场景
- 🚀 **轻量级**: 最小化依赖，快速启动
- 🐳 **多种部署**: 支持源码、pip、可执行文件、容器部署
- 📊 **监控支持**: 内置健康检查和日志管理

## 🛠️ 支持的工具

### 部门管理
- `zentao_get_departments` - 获取部门列表
- `zentao_get_department_users` - 获取部门用户

### 项目管理
- `zentao_get_projects` - 获取项目列表
- `zentao_get_project_stories` - 获取项目需求
- `zentao_get_project_tasks` - 获取项目任务
- `zentao_get_project_bugs` - 获取项目Bug

### Bug管理
- `zentao_get_bugs_by_date_range` - 按时间范围查询Bug
- `zentao_get_bug_details` - 获取Bug详情

### 需求管理
- `zentao_get_story_details` - 获取需求详情

### 任务管理
- `zentao_get_task_details` - 获取任务详情

### 用户管理
- `zentao_get_user_info` - 获取用户信息

*更多工具请参考后端服务文档*

## 📁 项目结构

```
zentao-mcp-client/
├── zentao_mcp/           # 核心代码
│   ├── cli.py           # 命令行接口
│   ├── config.py        # 配置管理
│   ├── proxy.py         # 代理服务
│   └── ...
├── config/              # 配置文件
│   ├── environments/    # 环境配置
│   ├── docker-compose.yml
│   └── Dockerfile
├── tests/               # 测试代码
├── deploy.sh           # 统一部署脚本
├── build.sh            # 构建脚本
└── README.md           # 项目文档
```

## 🚀 部署选项

### 开发环境
- **配置**: 源码运行 + STDIO模式
- **命令**: `./deploy.sh dev start`
- **特点**: 快速启动，实时代码修改

### 测试环境
- **配置**: 容器部署 + HTTP模式
- **命令**: `./deploy.sh test deploy --mode http`
- **特点**: 接近生产环境的部署方式

### 生产环境
- **配置**: 容器部署 + HTTP模式
- **命令**: `./deploy.sh prod deploy --mode http`
- **特点**: 高性能，容器化部署

## 🔍 故障排除

### 常见问题

1. **配置问题**
   ```bash
   # 检查配置状态
   zentao-mcp config show

   # 验证配置
   zentao-mcp config check
   ```

2. **连接问题**
   ```bash
   # 检查后端服务是否可达
   curl http://your-backend-url/health

   # 查看详细日志
   zentao-mcp start --mode http --verbose
   ```

3. **权限问题**
   ```bash
   # 检查API Key是否正确
   zentao-mcp config show

   # 重新配置
   zentao-mcp configure
   ```

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件
