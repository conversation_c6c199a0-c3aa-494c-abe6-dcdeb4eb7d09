# Zentao MCP Client 部署指南

## 📋 概述

本指南详细介绍了 Zentao MCP Client 的各种部署方式和配置选项，帮助您在不同环境中快速部署和使用客户端。

## 🎯 部署架构

### 环境分类
- **dev**: 开发环境 - 源码运行，快速迭代
- **test**: 测试环境 - 容器部署，接近生产
- **prod**: 生产环境 - 容器部署，高性能

### 部署类型
- **source**: 源码运行 - 使用 `uv` 管理依赖
- **pip**: pip安装 - 安装到Python环境
- **executable**: 可执行文件 - PyInstaller构建的二进制文件
- **container**: 容器部署 - Docker/Podman容器化

### 运行模式
- **stdio**: 标准输入输出 - 适合IDE集成
- **http**: HTTP服务 - 提供REST API
- **sse**: 服务器发送事件 - 支持实时推送

## 🚀 快速部署

### 开发环境（推荐新手）
```bash
cd zentao-mcp-client
./deploy.sh dev start
```

### 测试环境
```bash
cd zentao-mcp-client
./deploy.sh test deploy --mode http --port 8080
```

### 生产环境
```bash
cd zentao-mcp-client
./deploy.sh prod deploy --mode http --port 8080
```

## ⚙️ 统一部署脚本详解

### 基本语法
```bash
./deploy.sh <环境> <动作> [选项]
```

### 支持的动作
- `deploy`: 完整部署（构建+启动）
- `build`: 仅构建制品
- `start`: 启动服务
- `stop`: 停止服务
- `restart`: 重启服务
- `status`: 查看状态
- `config`: 配置向导
- `test`: 连接测试
- `clean`: 清理环境

### 常用选项
- `--type=<类型>`: 指定部署类型（source/pip/executable/container）
- `--mode=<模式>`: 指定运行模式（stdio/http/sse）
- `--host=<地址>`: HTTP服务监听地址
- `--port=<端口>`: 服务端口
- `--force`: 强制重建
- `--verbose`: 详细输出

## 📝 配置管理

### 环境变量配置文件
```
config/environments/
├── dev.env     # 开发环境配置
├── test.env    # 测试环境配置
└── prod.env    # 生产环境配置
```

### 配置文件示例
```bash
# config/environments/dev.env
ZENTAO_MCP_BACKEND_URL=http://localhost:8000
ZENTAO_MCP_API_KEY=dev-api-key-for-testing
CLIENT_MODE=http
CLIENT_HOST=0.0.0.0
CLIENT_PORT=8080
LOG_LEVEL=DEBUG
```

### 配置优先级
1. 环境变量（最高）
2. 命令行参数
3. 用户目录配置（最低）

## 🐳 容器部署

### Docker Compose配置
```yaml
# config/docker-compose.yml
version: '3.8'
services:
  client:
    build:
      context: ../
      dockerfile: config/Dockerfile
    environment:
      - ZENTAO_MCP_BACKEND_URL=${ZENTAO_MCP_BACKEND_URL}
      - ZENTAO_MCP_API_KEY=${ZENTAO_MCP_API_KEY}
    ports:
      - "${CLIENT_PORT:-8080}:8080"
```

### 容器部署命令
```bash
# 构建并启动容器
./deploy.sh test deploy --type container --mode http

# 仅构建容器镜像
./deploy.sh test build --type container

# 停止容器服务
./deploy.sh test stop --type container
```

## 🔧 高级配置

### 自定义配置
```bash
# 交互式配置所有参数
./deploy.sh

# 选择 "2. 交互式配置"
# 按提示选择环境、部署类型、运行模式等
```

### 环境变量设置
```bash
# 临时设置
export ZENTAO_MCP_BACKEND_URL="http://your-server:8000"
export ZENTAO_MCP_API_KEY="your-api-key"

# 永久设置（添加到 ~/.bashrc 或 ~/.zshrc）
echo 'export ZENTAO_MCP_BACKEND_URL="http://your-server:8000"' >> ~/.bashrc
echo 'export ZENTAO_MCP_API_KEY="your-api-key"' >> ~/.bashrc
```

## 📊 监控和日志

### 服务状态检查
```bash
# 检查服务状态
./deploy.sh dev status

# 检查容器状态
./deploy.sh test status --type container
```

### 日志查看
```bash
# 查看实时日志
zentao-mcp logs --follow

# 查看历史日志
zentao-mcp logs --lines 100
```

## 🔍 故障排除

### 常见问题

1. **端口占用**
   ```bash
   # 检查端口占用
   lsof -i :8080
   
   # 使用其他端口
   ./deploy.sh dev start --port 8081
   ```

2. **权限问题**
   ```bash
   # 可执行文件权限
   sudo cp dist/zentao-mcp /usr/local/bin/
   sudo chmod +x /usr/local/bin/zentao-mcp
   ```

3. **容器引擎问题**
   ```bash
   # 检查Docker状态
   docker --version
   docker-compose --version
   
   # 或检查Podman
   podman --version
   podman-compose --version
   ```

4. **依赖问题**
   ```bash
   # 重新安装依赖
   ./deploy.sh dev clean --type pip
   ./deploy.sh dev build --type pip
   ```

## 📋 部署检查清单

### 部署前检查
- [ ] Python 3.10+ 已安装
- [ ] 网络连接正常
- [ ] 后端服务可访问
- [ ] API Key 已获取

### 开发环境检查
- [ ] uv 已安装
- [ ] 源码完整
- [ ] 配置文件存在

### 生产环境检查
- [ ] 容器引擎已安装
- [ ] 环境变量已设置
- [ ] 网络端口已开放
- [ ] 监控系统已配置

## 🎯 最佳实践

1. **开发环境**: 使用源码运行，便于调试
2. **测试环境**: 使用容器部署，模拟生产环境
3. **生产环境**: 使用容器部署，确保稳定性
4. **配置管理**: 使用环境变量，避免硬编码
5. **监控日志**: 定期检查服务状态和日志
6. **安全考虑**: 妥善保管API Key，定期轮换

## 📞 技术支持

如遇到问题，请：
1. 查看本部署指南
2. 检查项目README.md
3. 查看错误日志
4. 提交Issue到项目仓库
