# 测试环境配置文件

# 客户端配置
ZENTAO_MCP_BACKEND_URL=http://localhost:8000
ZENTAO_MCP_API_KEY=test-api-key-for-testing
CLIENT_MODE=http
CLIENT_HOST=0.0.0.0
CLIENT_PORT=8080
LOG_LEVEL=INFO
ENVIRONMENT=testing

# 容器配置
CONTAINER_NAME=zentao-client-test
NETWORK_NAME=zentao-test-network
VOLUME_PREFIX=zentao-client-test

# Docker Compose 资源配置
MEMORY_LIMIT=256M
CPU_LIMIT=0.25
MEMORY_RESERVE=128M
CPU_RESERVE=0.1
HEALTH_START_PERIOD=30s
REPLICAS=1
