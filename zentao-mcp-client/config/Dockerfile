# 多阶段构建 Dockerfile - 构建可执行文件并运行
# 支持 test 和 prod 环境，通过环境变量控制差异
ARG PIP_INDEX_URL=https://pypi.tuna.tsinghua.edu.cn/simple
ARG PIP_TRUSTED_HOST=pypi.tuna.tsinghua.edu.cn
ARG APT_MIRROR=mirrors.tuna.tsinghua.edu.cn

# ============================================================================
# 阶段1: 构建阶段 - 构建可执行文件
# ============================================================================
FROM python:3.11-slim as builder

# 重新声明ARG变量（FROM之后需要重新声明）
ARG PIP_INDEX_URL
ARG PIP_TRUSTED_HOST
ARG APT_MIRROR

WORKDIR /app

# 配置时区和语言环境
ENV TZ=Asia/Shanghai \
    LANG=zh_CN.UTF-8 \
    LC_ALL=zh_CN.UTF-8 \
    DEBIAN_FRONTEND=noninteractive

# 配置pip镜像源和APT镜像源
ENV PIP_INDEX_URL=${PIP_INDEX_URL} \
    PIP_TRUSTED_HOST=${PIP_TRUSTED_HOST} \
    APT_MIRROR=${APT_MIRROR}

# 新增下面这行，为所有 uv 命令配置默认镜像源
ENV UV_INDEX_URL=${PIP_INDEX_URL}

# 配置APT镜像源并安装构建依赖
RUN set -eux; \
    # 完全替换APT源配置
    rm -rf /etc/apt/sources.list.d/*; \
    mkdir -p /etc/apt; \
    # 配置清华大学镜像源（使用trixie源，与backend保持一致）
    echo "deb https://${APT_MIRROR}/debian/ trixie main" > /etc/apt/sources.list; \
    echo "deb https://${APT_MIRROR}/debian/ trixie-updates main" >> /etc/apt/sources.list; \
    echo "deb https://${APT_MIRROR}/debian-security/ trixie-security main" >> /etc/apt/sources.list; \
    # 清理可能存在的其他源文件
    find /etc/apt -name "*.list" -not -name "sources.list" -delete || true; \
    # 更新包列表并安装构建依赖
    apt-get update; \
    apt-get install -y --no-install-recommends gcc g++ libc6-dev locales; \
    echo "zh_CN.UTF-8 UTF-8" > /etc/locale.gen; \
    locale-gen; \
    # 清理APT缓存
    apt-get clean; \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*;

# 复制依赖文件
COPY pyproject.toml uv.lock ./

# 安装uv和Python依赖
RUN set -eux; \
    if [ -n "${PIP_INDEX_URL}" ] && [ -n "${PIP_TRUSTED_HOST}" ]; then \
        pip install --no-cache-dir -i ${PIP_INDEX_URL} --trusted-host ${PIP_TRUSTED_HOST} uv; \
    else \
        pip install --no-cache-dir uv; \
    fi; \
    # 同步依赖
    uv sync --all-extras --no-cache; \
    # 清理pip缓存
    pip cache purge || true; \
    # 清理uv缓存
    uv cache clean || true;

# 复制应用代码
COPY . .

# 构建可执行文件
RUN set -eux; \
    # 使用 --paths 告诉PyInstaller源码在哪里
    uv run python -m PyInstaller \
        --onefile \
        --name zentao-mcp \
        --paths zentao_mcp \
        --add-data "zentao_mcp:zentao_mcp" \
        --copy-metadata fastmcp \
        --hidden-import zentao_mcp.cli \
        --hidden-import zentao_mcp.config \
        --hidden-import zentao_mcp.proxy \
        --hidden-import zentao_mcp.logging_config \
        --hidden-import zentao_mcp.log_viewer \
        --hidden-import fastmcp \
        --hidden-import httpx \
        --hidden-import click \
        --hidden-import pydantic \
        --hidden-import starlette.requests \
        --hidden-import starlette.responses \
        --hidden-import configparser \
        --hidden-import pathlib \
        --hidden-import asyncio \
        --exclude-module tkinter \
        --exclude-module matplotlib \
        --exclude-module numpy \
        --exclude-module pandas \
        --exclude-module scipy \
        --exclude-module PIL \
        --exclude-module PyQt5 \
        --exclude-module PyQt6 \
        --exclude-module PySide2 \
        --exclude-module PySide6 \
        --clean \
        --noconfirm \
        zentao_mcp/cli.py; \
    # 验证可执行文件存在
    ls -la dist/zentao-mcp; \
    # 测试可执行文件
    ./dist/zentao-mcp --help || echo "Build completed"

# ============================================================================
# 阶段2: 运行阶段 - 轻量级运行环境
# ============================================================================
FROM python:3.11-slim as runtime

# 重新声明ARG变量
ARG APT_MIRROR=mirrors.tuna.tsinghua.edu.cn

# 配置时区和语言环境
ENV TZ=Asia/Shanghai \
    LANG=zh_CN.UTF-8 \
    LC_ALL=zh_CN.UTF-8 \
    DEBIAN_FRONTEND=noninteractive

WORKDIR /app

# 安装运行时依赖
RUN set -eux; \
    # 临时使用 http 协议来安装 ca-certificates
    rm -rf /etc/apt/sources.list.d/*; \
    mkdir -p /etc/apt; \
    echo "deb https://${APT_MIRROR}/debian/ trixie main" > /etc/apt/sources.list; \
    echo "deb https://${APT_MIRROR}/debian/ trixie-updates main" >> /etc/apt/sources.list; \
    echo "deb https://${APT_MIRROR}/debian-security/ trixie-security main" >> /etc/apt/sources.list; \
    apt-get update; \
    apt-get install -y --no-install-recommends \
        curl \
        ca-certificates \
        locales; \
    # 配置语言环境
    echo "zh_CN.UTF-8 UTF-8" > /etc/locale.gen; \
    locale-gen; \
    # 清理APT缓存
    apt-get clean; \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*;

# 从构建阶段复制可执行文件
COPY --from=builder /app/dist/zentao-mcp /usr/local/bin/zentao-mcp

# 创建必要目录
RUN mkdir -p logs

# 创建非root用户
RUN useradd --create-home --shell /bin/bash app \
    && chown -R app:app /app \
    && chmod +x /usr/local/bin/zentao-mcp

# 切换到非root用户
USER app

# 暴露端口（HTTP模式）
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 启动命令（使用可执行文件）
CMD ["zentao-mcp", "start", "--mode", "http", "--host", "0.0.0.0", "--port", "8080"]