# 决策日志

## 最新决策

### [2025-09-10] zentao-mcp-client包结构修复决策
**问题**: 客户端运行时出现模块导入失败错误
**根本原因**: 包结构不一致 - pyproject.toml配置使用`zentao_mcp`（下划线），但实际目录为`zentao-mcp`（连字符）
**解决方案**: 重命名目录从`zentao-mcp`改为`zentao_mcp`，符合Python包命名规范
**影响**: 
- ✅ 修复了模块导入问题
- ✅ 符合Python包管理最佳实践
- ✅ 与pyproject.toml配置完全一致
- ✅ 可执行文件构建成功
- ✅ Docker构建正常
**相关文件**: Dockerfile, build_executable.py, main.py（新增）

## 历史决策

### [2025-09-03] 服务端日志系统架构决策
**决策**: 采用结构化日志与中间件集成的架构模式
**理由**: 实现完整的请求追踪、异常处理和性能监控
**实现**: JSON格式日志、请求ID追踪、敏感信息过滤、多层中间件架构

### [2025-09-01] 服务端架构偏差修复决策
**决策**: 统一密码哈希机制、数据模型命名、API路径规范
**理由**: 解决架构不一致问题，提高系统稳定性
**实现**: 使用bcrypt密码哈希、统一命名规范、明确认证机制分离

### [2025-08-29] Python环境管理决策
**决策**: 使用uv作为Python依赖管理工具
**理由**: 更快的依赖解析和安装速度，更好的锁文件管理
**实现**: 清理旧的pip/poetry环境，统一使用uv管理所有Python子项目

### [2025-08-28] FastMCP到FastAPI迁移决策
**决策**: 完全移除MCP装饰器，创建完整的服务层架构
**理由**: 实现更好的代码组织、依赖注入和测试能力
**实现**: 8个服务类、依赖注入、异步架构、完整的API层
